//
//  PitchDetector.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//
//  Implementation of the pitch detection algorithm from the Acapella paper
//  Based on autocorrelation in the time domain with audio energy processing

import Foundation
import Accelerate

class PitchDetector {
    // MARK: - Configuration
    private var configuration: AlgorithmConfiguration

    // MARK: - Configuration Parameters (computed from configuration)
    private var sampleRate: Float { configuration.sampleRate }
    private var minFreq: Int { configuration.minFreq }
    private var maxFreq: Int { configuration.maxFreq }
    private var freqAccuracy: Float { configuration.freqAccuracy }
    private var lowerFormantFreq: Int { configuration.lowerFormantFreq }
    private var decayRate: Float { configuration.decayRate }
    private var minAmp: Float { configuration.minAmp }
    
    // MARK: - Processing Parameters (computed from configuration)
    private var downsampleFactor: Int { configuration.downsampleFactor }
    private var downsampledRate: Float { configuration.downsampledRate }
    private var smoothingFilterSize: Int { configuration.smoothingFilterSize }
    
    // MARK: - State Variables
    private var movingAvePeriod: Float = 0.0
    private var movingAveAmplitude: Float = 0.0
    private var movingAveDerivative: Float = 0.0
    private var runLength: Int = 0
    private var maxRunLength: Int { configuration.maxRunLength }

    // MARK: - Economical Autocorrelation State
    private var lastDetectedPitch: Float = 0.0
    private var lastWavelength: Int = 0
    private var previousMovingAvePeriod: Float = 0.0  // For derivative calculation
    private var lastAutocorrelationPosition: Int = 0  // Track position for "one wavelength ahead"

    // MARK: - Target Range Maintenance
    private var targetMinFreq: Float
    private var targetMaxFreq: Float
    private var targetMinPeriod: Int = 0
    private var targetMaxPeriod: Int = 0
    
    // MARK: - Audio Processing Buffers
    private var audioBuffer: [Float] = []
    private var processedBuffer: [Float] = []
    private var targetBufferLength: Int { configuration.targetBufferLength }

    // MARK: - Session Management
    private var sessionSampleCounter: Int = 0
    private var saveResults: Bool { configuration.saveResults }

    // MARK: - Result Saving (Paper Specification)
    private var detectionHistory: [DetectionResult] = []
    private var correlationHistory: [CorrelationData] = []

    // Data structures for result saving
    private struct DetectionResult: Codable {
        let timestamp: Double
        let sampleIndex: Int
        let detectedPitch: Float
        let correlation: Float
        let searchStrategy: String
        let runLength: Int
        let movingAvePeriod: Float
        let movingAveAmplitude: Float
        let movingAveDerivative: Float
    }

    private struct CorrelationData: Codable {
        let timestamp: Double
        let sampleIndex: Int
        let lag: Int
        let correlation: Float
        let searchType: String // "economical", "coarse", "fine"
    }
    
    // MARK: - Initialization

    /// Initialize with configuration object
    init(configuration: AlgorithmConfiguration) {
        self.configuration = configuration

        // Initialize target range to absolute bounds
        self.targetMinFreq = Float(configuration.minFreq)
        self.targetMaxFreq = Float(configuration.maxFreq)

        // Initialize target periods based on target frequency range
        updateTargetPeriods()

        print("PitchDetector initialized with configuration:")
        print("  Sample rate: \(configuration.sampleRate) Hz")
        print("  Downsample factor: \(configuration.downsampleFactor)")
        print("  Downsampled rate: \(configuration.downsampledRate) Hz")
        print("  Target frequency range: \(configuration.minFreq)-\(configuration.maxFreq) Hz")
        print("  Initial target range: \(targetMinFreq)-\(targetMaxFreq) Hz")
        print("  Smoothing filter size: \(configuration.smoothingFilterSize)")
    }

    /// Legacy initializer for backward compatibility
    convenience init(sampleRate: Float, minFreq: Int, maxFreq: Int, freqAccuracy: Float,
                    lowerFormantFreq: Int, decayRate: Float, minAmp: Float, saveResults: Bool = false) {
        var config = AlgorithmConfiguration.standard
        config.sampleRate = sampleRate
        config.minFreq = minFreq
        config.maxFreq = maxFreq
        config.freqAccuracy = freqAccuracy
        config.lowerFormantFreq = lowerFormantFreq
        config.decayRate = decayRate
        config.minAmp = minAmp
        config.saveResults = saveResults

        self.init(configuration: config)
    }

    /// Update configuration at runtime
    func updateConfiguration(_ newConfiguration: AlgorithmConfiguration) {
        self.configuration = newConfiguration

        // Reset target range to new bounds
        self.targetMinFreq = Float(newConfiguration.minFreq)
        self.targetMaxFreq = Float(newConfiguration.maxFreq)
        updateTargetPeriods()

        print("🔄 Configuration updated - new settings applied")
    }
    
    // MARK: - Session Management
    func startNewSession() {
        // Reset all state variables as specified in paper
        movingAvePeriod = 0.0
        movingAveAmplitude = 0.0
        movingAveDerivative = 0.0
        runLength = 0
        lastDetectedPitch = 0.0
        lastWavelength = 0
        previousMovingAvePeriod = 0.0
        lastAutocorrelationPosition = 0
        sessionSampleCounter = 0

        // Reset target range to absolute bounds
        targetMinFreq = Float(minFreq)
        targetMaxFreq = Float(maxFreq)
        updateTargetPeriods()

        // Clear processing buffers
        audioBuffer.removeAll()
        processedBuffer.removeAll()

        // Clear result saving data
        detectionHistory.removeAll()
        correlationHistory.removeAll()

        print("🔄 New session started - all state variables reset")
    }

    // MARK: - Main Processing Method
    func processChunk(_ audioData: [Float], thisFirstNativeInd: Int = 0, numElements: Int? = nil) -> Float {
        let elementCount = numElements ?? audioData.count

        // Paper specification: thisFirstNativeInd tracks absolute sample position from session start
        // This is critical for proper incremental processing pipeline state management
        let absoluteSampleIndex = thisFirstNativeInd + elementCount
        sessionSampleCounter = absoluteSampleIndex

        print("🔧 PitchDetector.processChunk - Input samples: \(audioData.count), Absolute index: \(absoluteSampleIndex)")

        // Check if we have enough data
        guard audioData.count > configuration.minDataCheck else {
            print("❌ Not enough audio data: \(audioData.count) samples (need \(configuration.minDataCheck))")
            return 0.0
        }

        // Step 1: Subtract smoothed version from raw audio
        let filteredAudio = subtractSmoothedVersion(audioData)
        print("🔧 Step 1 complete - Filtered samples: \(filteredAudio.count)")

        // Step 2: Compute the square of the filtered signal
        let squaredAudio = computeSquare(filteredAudio)
        print("🔧 Step 2 complete - Squared samples: \(squaredAudio.count)")

        // Step 3: Downsample by averaging contiguous sets
        let downsampledAudio = downsample(squaredAudio)
        print("🔧 Step 3 complete - Downsampled samples: \(downsampledAudio.count)")

        // Step 4: Apply Gaussian smoothing
        let smoothedAudio = applyGaussianSmoothing(downsampledAudio)
        print("🔧 Step 4 complete - Smoothed samples: \(smoothedAudio.count)")

        // Step 5: Accumulate processed data for better pitch detection
        processedBuffer.append(contentsOf: smoothedAudio)

        // Keep buffer at target length (sliding window)
        if processedBuffer.count > targetBufferLength {
            let excess = processedBuffer.count - targetBufferLength
            processedBuffer.removeFirst(excess)
        }

        print("🔧 Accumulated buffer length: \(processedBuffer.count) (target: \(targetBufferLength))")

        // Only perform pitch detection if we have enough accumulated data
        guard processedBuffer.count >= targetBufferLength else {
            print("🔧 Accumulating data... need \(targetBufferLength - processedBuffer.count) more samples")
            return 0.0
        }

        // Step 6: Compute autocorrelation on accumulated data
        let detectedPitch = computeAutocorrelation(processedBuffer)
        print("🔧 Step 5 complete - Detected pitch: \(detectedPitch) Hz")

        return detectedPitch
    }
    
    // MARK: - Audio Processing Steps
    
    /// Step 1: Subtract smoothed version from raw audio signal
    private func subtractSmoothedVersion(_ audioData: [Float]) -> [Float] {
        let smoothedData = computeMovingAverage(audioData, windowSize: smoothingFilterSize)
        
        var result = [Float](repeating: 0.0, count: audioData.count)
        vDSP_vsub(smoothedData, 1, audioData, 1, &result, 1, vDSP_Length(audioData.count))
        
        return result
    }
    
    /// Step 2: Compute the square of the signal
    private func computeSquare(_ audioData: [Float]) -> [Float] {
        var result = [Float](repeating: 0.0, count: audioData.count)
        vDSP_vsq(audioData, 1, &result, 1, vDSP_Length(audioData.count))
        return result
    }
    
    /// Step 3: Downsample by averaging contiguous sets of samples
    private func downsample(_ audioData: [Float]) -> [Float] {
        let outputLength = audioData.count / downsampleFactor
        var result = [Float](repeating: 0.0, count: outputLength)
        
        for i in 0..<outputLength {
            let startIndex = i * downsampleFactor
            let endIndex = min(startIndex + downsampleFactor, audioData.count)
            
            var sum: Float = 0.0
            for j in startIndex..<endIndex {
                sum += audioData[j]
            }
            result[i] = sum / Float(endIndex - startIndex)
        }
        
        return result
    }
    
    /// Step 4: Apply Gaussian smoothing filter
    private func applyGaussianSmoothing(_ audioData: [Float]) -> [Float] {
        // Paper specifies: sigma = 0.2 * 45 * fupper / Fs
        // But we need to use our actual downsample factor and original sample rate
        let sigma = 0.2 * Float(downsampleFactor) * Float(maxFreq) / sampleRate

        // Convert sigma to downsampled domain
        let sigmaDownsampled = sigma * Float(downsampleFactor)
        let kernelSize = max(3, Int(6 * sigmaDownsampled)) // Ensure minimum kernel size

        print("🔧 Gaussian smoothing - sigma: \(sigma), sigmaDownsampled: \(sigmaDownsampled), kernelSize: \(kernelSize)")

        let gaussianKernel = createGaussianKernel(size: kernelSize, sigma: sigmaDownsampled)
        return convolve(audioData, with: gaussianKernel)
    }
    
    /// Step 5: Compute autocorrelation and detect pitch (Economical Implementation)
    private func computeAutocorrelation(_ audioData: [Float]) -> Float {
        let minPeriod = Int(downsampledRate / Float(maxFreq))
        // Allow detection down to 7Hz instead of 10Hz
        let effectiveMinFreq: Float = 7.0 // Hz
        let maxPeriod = min(Int(downsampledRate / effectiveMinFreq), audioData.count - 1)

        // Debug: Check the processed audio data
        let dataMin = audioData.min() ?? 0.0
        let dataMax = audioData.max() ?? 0.0
        let dataAvg = audioData.reduce(0, +) / Float(audioData.count)
        let dataVariance = audioData.map { ($0 - dataAvg) * ($0 - dataAvg) }.reduce(0, +) / Float(audioData.count)

        print("🔍 Autocorrelation - Data length: \(audioData.count), MinPeriod: \(minPeriod), MaxPeriod: \(maxPeriod)")
        print("🔍 Data stats - Min: \(dataMin), Max: \(dataMax), Avg: \(dataAvg), Variance: \(dataVariance)")

        guard minPeriod < maxPeriod && maxPeriod > 0 && audioData.count > minPeriod else {
            print("❌ Invalid period range or insufficient data - need at least \(minPeriod) samples, have \(audioData.count)")
            return 0.0
        }

        // Check if we have any signal variance (lowered threshold for better sensitivity)
        guard dataVariance > 1e-12 else {
            print("❌ No signal variance - data is too flat for correlation")
            return 0.0
        }

        var bestCorrelation: Float = 0.0
        var bestPeriod: Int = 0
        var correlationCount = 0
        var searchStrategy = "full"

        // ECONOMICAL AUTOCORRELATION: Search based on previous detection
        if lastDetectedPitch > 0.0 && lastWavelength > 0 {
            // Paper specification: "Once a pitch has been detected, the next autocorrelation is computed
            // at a location one wavelength (of the last pitch detected) ahead of the previous point"
            let oneWavelengthAhead = lastAutocorrelationPosition + lastWavelength
            let searchWindow = max(5, Int(0.2 * Float(lastWavelength))) // ±20% window

            // Use derivative to refine search window (Paper: "Update the derivative of the moving average,
            // in order to refine the search window at the next iteration")
            let derivativeAdjustment = Int(movingAveDerivative * 2.0) // Scale derivative for search adjustment
            let predictedLag = oneWavelengthAhead + derivativeAdjustment

            let economicalMinLag = max(minPeriod, predictedLag - searchWindow)
            let economicalMaxLag = min(maxPeriod, predictedLag + searchWindow)

            print("🎯 Economical search: one wavelength ahead \(oneWavelengthAhead), derivative adj \(derivativeAdjustment), predicted lag \(predictedLag), window [\(economicalMinLag), \(economicalMaxLag)]")

            // Validate range before proceeding
            if economicalMinLag > economicalMaxLag {
                print("⚠️ Invalid economical search range: \(economicalMinLag) > \(economicalMaxLag), skipping economical search")
                searchStrategy = "fallback"
            }

            if economicalMinLag <= economicalMaxLag {
                searchStrategy = "economical"

                // Search in narrow window first
                for lag in economicalMinLag...economicalMaxLag {
                let correlation = computeCorrelationAtLag(audioData, lag: lag, searchType: "economical")
                correlationCount += 1

                if correlation > bestCorrelation {
                    bestCorrelation = correlation
                    bestPeriod = lag
                }

                    // Debug: Show correlation values in economical search
                    if correlation > 0.3 {
                        print("🎯 Economical lag \(lag): correlation = \(correlation)")
                    }
                }

                // If no good correlation found in narrow window, fall back to full search
                if bestCorrelation < 0.6 {
                    print("🔄 Economical search failed (best: \(bestCorrelation)), falling back to full search")
                    searchStrategy = "fallback"
                    bestCorrelation = 0.0
                    bestPeriod = 0
                    correlationCount = 0
                }
            }
        }

        // Full search with two-stage refinement (either initial search or fallback from economical)
        if searchStrategy != "economical" || bestCorrelation < 0.6 {
            print("🔍 Performing two-stage autocorrelation search")

            // STAGE 1: Coarse search (configurable step size for speed)
            var coarseBestCorrelation: Float = 0.0
            var coarseBestLag: Int = 0
            let coarseStep = configuration.coarseStep

            for lag in stride(from: minPeriod, through: maxPeriod, by: coarseStep) {
                let correlation = computeCorrelationAtLag(audioData, lag: lag, searchType: "coarse")
                correlationCount += 1

                if correlation > coarseBestCorrelation {
                    coarseBestCorrelation = correlation
                    coarseBestLag = lag
                }

                // Debug: Show some coarse correlation values
                if correlationCount <= 3 || correlation > 0.3 {
                    print("🔍 Coarse search lag \(lag): correlation = \(correlation)")
                }
            }

            // STAGE 2: Fine search around coarse maximum
            if coarseBestLag > 0 {
                let fineSearchWindow = configuration.fineSearchWindow
                let fineMinLag = max(minPeriod, coarseBestLag - fineSearchWindow)
                let fineMaxLag = min(maxPeriod, coarseBestLag + fineSearchWindow)

                print("🔍 Fine search around lag \(coarseBestLag) in range [\(fineMinLag), \(fineMaxLag)]")

                // Validate range before proceeding
                if fineMinLag <= fineMaxLag {
                    for lag in fineMinLag...fineMaxLag {
                    let correlation = computeCorrelationAtLag(audioData, lag: lag, searchType: "fine")
                    correlationCount += 1

                    if correlation > bestCorrelation {
                        bestCorrelation = correlation
                        bestPeriod = lag
                    }

                        // Debug: Show fine search values
                        if correlation > 0.3 {
                            print("🔍 Fine search lag \(lag): correlation = \(correlation)")
                        }
                    }
                } else {
                    print("⚠️ Invalid fine search range: \(fineMinLag) > \(fineMaxLag), using coarse result")
                    bestCorrelation = coarseBestCorrelation
                    bestPeriod = coarseBestLag
                }
            } else {
                // Fallback to simple search if coarse search failed
                bestCorrelation = coarseBestCorrelation
                bestPeriod = coarseBestLag
            }
        }

        print("🔍 Best correlation: \(bestCorrelation) at period \(bestPeriod) (threshold: 0.6) [strategy: \(searchStrategy)]")

        if bestPeriod > 0 && bestCorrelation > configuration.correlationThreshold {
            let detectedFreq = downsampledRate / Float(bestPeriod)

            // Update economical search state
            lastDetectedPitch = detectedFreq
            lastWavelength = bestPeriod
            lastAutocorrelationPosition = bestPeriod  // Update position for "one wavelength ahead"

            // Update target range based on detected frequency
            updateTargetRange(detectedFreq: detectedFreq)

            updateMovingAverages(period: Float(bestPeriod), amplitude: bestCorrelation)

            // Save results if enabled (Paper specification)
            if saveResults {
                saveDetectionResult(
                    detectedPitch: detectedFreq,
                    correlation: bestCorrelation,
                    searchStrategy: searchStrategy,
                    sampleIndex: sessionSampleCounter
                )
            }

            print("✅ Pitch detected: \(detectedFreq) Hz (updating economical state and target range)")
            return detectedFreq
        }

        print("❌ No pitch detected - best correlation \(bestCorrelation) below threshold 0.6")
        return 0.0 // No pitch detected
    }
    
    // MARK: - Helper Methods
    
    private func computeMovingAverage(_ data: [Float], windowSize: Int) -> [Float] {
        guard windowSize > 0 && windowSize <= data.count else { return data }
        
        var result = [Float](repeating: 0.0, count: data.count)
        let halfWindow = windowSize / 2
        
        for i in 0..<data.count {
            let startIndex = max(0, i - halfWindow)
            let endIndex = min(data.count, i + halfWindow + 1)
            
            var sum: Float = 0.0
            for j in startIndex..<endIndex {
                sum += data[j]
            }
            result[i] = sum / Float(endIndex - startIndex)
        }
        
        return result
    }
    
    private func createGaussianKernel(size: Int, sigma: Float) -> [Float] {
        var kernel = [Float](repeating: 0.0, count: size)
        let center = Float(size - 1) / 2.0
        var sum: Float = 0.0
        
        for i in 0..<size {
            let x = Float(i) - center
            let value = exp(-(x * x) / (2.0 * sigma * sigma))
            kernel[i] = value
            sum += value
        }
        
        // Normalize kernel
        for i in 0..<size {
            kernel[i] /= sum
        }
        
        return kernel
    }
    
    private func convolve(_ signal: [Float], with kernel: [Float]) -> [Float] {
        let signalLength = signal.count
        let kernelLength = kernel.count
        let outputLength = signalLength
        
        var result = [Float](repeating: 0.0, count: outputLength)
        
        for i in 0..<outputLength {
            var sum: Float = 0.0
            for j in 0..<kernelLength {
                let signalIndex = i - j + kernelLength / 2
                if signalIndex >= 0 && signalIndex < signalLength {
                    sum += signal[signalIndex] * kernel[j]
                }
            }
            result[i] = sum
        }
        
        return result
    }
    
    private func computeCorrelationAtLag(_ data: [Float], lag: Int, searchType: String = "unknown") -> Float {
        guard lag < data.count else { return 0.0 }

        let length = data.count - lag
        guard length > 0 else { return 0.0 }

        var correlation: Float = 0.0
        var norm1: Float = 0.0
        var norm2: Float = 0.0

        for i in 0..<length {
            let val1 = data[i]
            let val2 = data[i + lag]

            correlation += val1 * val2
            norm1 += val1 * val1
            norm2 += val2 * val2
        }

        let normProduct = sqrt(norm1 * norm2)
        let result = normProduct > 1e-10 ? correlation / normProduct : 0.0

        // Save correlation data if results saving is enabled
        if saveResults && result > 0.3 { // Only save significant correlations
            saveCorrelationData(lag: lag, correlation: result, searchType: searchType)
        }

        // Debug first few correlations
        if lag <= 30 {
            print("🔍 Lag \(lag): corr=\(correlation), norm1=\(norm1), norm2=\(norm2), result=\(result)")
        }

        return result
    }
    
    private func updateMovingAverages(period: Float, amplitude: Float) {
        // Store previous period for derivative calculation
        previousMovingAvePeriod = movingAvePeriod

        // RUN LENGTH REDUCTION LOGIC (Paper specification)
        // Check if pitch leap exceeds expected amount
        if movingAvePeriod > 0 {
            let currentFreq = downsampledRate / period
            let expectedFreq = downsampledRate / movingAvePeriod
            let pitchDifference = abs(currentFreq - expectedFreq)
            let leapThreshold = configuration.leapThreshold * expectedFreq

            if pitchDifference > leapThreshold {
                let leapFactor = Int(pitchDifference / leapThreshold)
                let oldRunLength = runLength
                runLength = max(0, runLength - leapFactor)
                print("🔄 Run length reduced: \(oldRunLength) → \(runLength) (leap: \(pitchDifference) Hz, threshold: \(leapThreshold) Hz)")

                // Reset moving averages if run length drops to 0
                if runLength == 0 {
                    movingAvePeriod = 0.0
                    movingAveAmplitude = 0.0
                    movingAveDerivative = 0.0
                    print("🔄 Moving averages reset due to run length = 0")
                    return
                }
            }
        }

        // Update run length based on algorithm from paper (increment if no leap)
        if runLength < maxRunLength {
            runLength += 1
        }

        // Compute mix value as specified in paper
        let mix = min(decayRate, 1.0 - 1.0 / Float(runLength))

        // Update moving averages
        movingAvePeriod = movingAvePeriod * mix + period * (1.0 - mix)
        movingAveAmplitude = movingAveAmplitude * mix + amplitude * (1.0 - mix)

        // Update derivative (change in period over time)
        if previousMovingAvePeriod > 0 {
            let periodChange = movingAvePeriod - previousMovingAvePeriod
            movingAveDerivative = movingAveDerivative * mix + periodChange * (1.0 - mix)
        }

        print("📊 Moving averages updated - Period: \(movingAvePeriod), Amplitude: \(movingAveAmplitude), RunLength: \(runLength)")
    }

    // MARK: - Target Range Management

    private func updateTargetPeriods() {
        targetMinPeriod = Int(downsampledRate / targetMaxFreq)
        targetMaxPeriod = Int(downsampledRate / targetMinFreq)
    }

    private func updateTargetRange(detectedFreq: Float) {
        // Update target range with ±25% window around detected frequency
        let rangeWindow: Float = 0.25
        let newMinFreq = detectedFreq * (1.0 - rangeWindow)
        let newMaxFreq = detectedFreq * (1.0 + rangeWindow)

        // Clamp to absolute bounds
        targetMinFreq = max(Float(minFreq), newMinFreq)
        targetMaxFreq = min(Float(maxFreq), newMaxFreq)

        // Update corresponding periods
        updateTargetPeriods()

        print("🎯 Target range updated: \(targetMinFreq)-\(targetMaxFreq) Hz (periods: \(targetMinPeriod)-\(targetMaxPeriod))")
    }

    // MARK: - Result Saving Methods (Paper Specification)

    private func saveDetectionResult(detectedPitch: Float, correlation: Float, searchStrategy: String, sampleIndex: Int) {
        let result = DetectionResult(
            timestamp: Date().timeIntervalSince1970,
            sampleIndex: sampleIndex,
            detectedPitch: detectedPitch,
            correlation: correlation,
            searchStrategy: searchStrategy,
            runLength: runLength,
            movingAvePeriod: movingAvePeriod,
            movingAveAmplitude: movingAveAmplitude,
            movingAveDerivative: movingAveDerivative
        )

        detectionHistory.append(result)

        // Limit history size to prevent memory issues
        if detectionHistory.count > 1000 {
            detectionHistory.removeFirst(detectionHistory.count - 1000)
        }

        print("💾 Detection result saved: \(detectedPitch) Hz at sample \(sampleIndex)")
    }

    private func saveCorrelationData(lag: Int, correlation: Float, searchType: String) {
        let data = CorrelationData(
            timestamp: Date().timeIntervalSince1970,
            sampleIndex: sessionSampleCounter,
            lag: lag,
            correlation: correlation,
            searchType: searchType
        )

        correlationHistory.append(data)

        // Limit history size to prevent memory issues
        if correlationHistory.count > 5000 {
            correlationHistory.removeFirst(correlationHistory.count - 5000)
        }
    }

    /// Export saved results to JSON for analysis (Paper specification)
    func exportResults() -> (detections: Data?, correlations: Data?) {
        guard saveResults else {
            print("⚠️ Result saving is disabled - no data to export")
            return (nil, nil)
        }

        let encoder = JSONEncoder()
        encoder.outputFormatting = .prettyPrinted

        var detectionsData: Data?
        var correlationsData: Data?

        do {
            detectionsData = try encoder.encode(detectionHistory)
            correlationsData = try encoder.encode(correlationHistory)
            print("✅ Results exported - Detections: \(detectionHistory.count), Correlations: \(correlationHistory.count)")
        } catch {
            print("❌ Failed to export results: \(error)")
        }

        return (detectionsData, correlationsData)
    }

    /// Save results to files (Paper specification)
    func saveResultsToFiles(baseURL: URL) throws {
        guard saveResults else {
            throw NSError(domain: "PitchDetector", code: 1, userInfo: [NSLocalizedDescriptionKey: "Result saving is disabled"])
        }

        let (detectionsData, correlationsData) = exportResults()

        if let detectionsData = detectionsData {
            let detectionsURL = baseURL.appendingPathComponent("pitch_detections.json")
            try detectionsData.write(to: detectionsURL)
            print("💾 Detection results saved to: \(detectionsURL.path)")
        }

        if let correlationsData = correlationsData {
            let correlationsURL = baseURL.appendingPathComponent("correlation_data.json")
            try correlationsData.write(to: correlationsURL)
            print("💾 Correlation data saved to: \(correlationsURL.path)")
        }
    }
}
