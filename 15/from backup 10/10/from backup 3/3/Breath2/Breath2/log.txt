✅ Loaded 9 recent sessions from file
PitchDetector initialized with configuration:
  Sample rate: 44100.0 Hz
  Downsample factor: 45
  Downsampled rate: 980.0 Hz
  Target frequency range: 7-40 Hz
  Initial target range: 7.0-40.0 Hz
  Smoothing filter size: 176
PressureCalculator initialized:
  Linear model: Pressure = -4.659 + 1.119 * Frequency
  Research equation: Pressure = -4.659 + 1.119 × Pitch
  r² = 0.886 across 9,993 data points
  Valid frequency range: 7.0-40.0 Hz
  Valid pressure range: 6.0-30.0 cm H2O
✅ Audio session configured successfully
✅ New therapy session started
🎬 startRecording called
🫁 Breath detection: Reset
🔧 Setting up audio engine...
Input format: <AVAudioFormat 0x1083bf520:  1 ch,  48000 Hz, Float32>
PitchDetector initialized with configuration:
  Sample rate: 48000.0 Hz
  Downsample factor: 45
  Downsampled rate: 1066.6666 Hz
  Target frequency range: 7-40 Hz
  Initial target range: 7.0-40.0 Hz
  Smoothing filter size: 192
🔄 New session started - all state variables reset
✅ Audio engine started successfully
App is being debugged, do not track this hang
Hang detected: 0.29s (debugger attached, not reporting)
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.00047954722, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 4800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 106 (target: 200)
🔍 Autocorrelation - Data length: 106, MinPeriod: 26, MaxPeriod: 105
🔍 Data stats - Min: 9.531802e-13, Max: 2.8204322e-08, Avg: 4.548519e-09, Variance: 3.2666706e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 4800)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.00053544174, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 9600
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 6.035902e-11, Max: 8.67922e-08, Avg: 1.1803624e-08, Variance: 1.3961522e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 9600)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0006225364, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 14400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 2.848551e-09, Max: 1.1976479e-07, Avg: 2.256543e-08, Variance: 2.8131555e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 14400)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0005433692, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 19200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 2.453397e-09, Max: 1.1976479e-07, Avg: 2.418408e-08, Variance: 3.472664e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 19200)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0007818949, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 24000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 4.1287236e-09, Max: 1.1179411e-07, Avg: 2.0784826e-08, Variance: 2.5888599e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 24000)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00045499892, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 28800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.6596106e-09, Max: 1.1179411e-07, Avg: 1.5432612e-08, Variance: 1.4390928e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 28800)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00047486555, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 33600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.5105952e-09, Max: 4.8832458e-08, Avg: 1.26146515e-08, Variance: 7.746752e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 33600)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00044460857, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 38400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.5105952e-09, Max: 4.8832458e-08, Avg: 1.2091468e-08, Variance: 7.613728e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 38400)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00055830233, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 43200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.7969999e-09, Max: 4.941323e-08, Avg: 1.0237788e-08, Variance: 5.3937324e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 43200)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0005947815, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 48000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.7905762e-09, Max: 5.4341587e-08, Avg: 1.12951275e-08, Variance: 6.5925574e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 48000)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.000393118, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 52800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 9.340078e-10, Max: 5.3692712e-08, Avg: 9.773572e-09, Variance: 3.869078e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 52800)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00057947205, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 57600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 9.340078e-10, Max: 6.193474e-08, Avg: 8.875565e-09, Variance: 4.9459227e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 57600)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0004914028, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 62400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 9.868679e-10, Max: 6.193474e-08, Avg: 8.78824e-09, Variance: 5.739938e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 62400)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0003794214, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 67200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 8.980467e-10, Max: 4.3604818e-08, Avg: 7.675869e-09, Variance: 3.0278195e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 67200)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00044348993, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 72000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 8.980467e-10, Max: 5.978123e-08, Avg: 8.364632e-09, Variance: 4.149894e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 72000)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00044263247, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 76800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.3954705e-09, Max: 5.978123e-08, Avg: 8.076949e-09, Variance: 4.3010487e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 76800)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00045303488, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 81600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.3954705e-09, Max: 3.205024e-08, Avg: 7.4425053e-09, Variance: 2.428082e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 81600)
� Calculated pressure: 0.0 cm H2O
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.0004786002, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 86400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.414019e-09, Max: 4.8856023e-08, Avg: 1.0769481e-08, Variance: 5.3439252e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 86400)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0018733637, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 91200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 2.4425586e-09, Max: 6.085297e-07, Avg: 2.5351271e-08, Variance: 3.6556345e-15
🔍 Performing autocorrelation search
🔍 Lag 26: corr=8.304567e-14, norm1=7.4139463e-13, norm2=8.543921e-13, result=0.0
🔍 Coarse search lag 26: correlation = 0.0
🔍 Lag 29: corr=9.741399e-14, norm1=3.1719313e-13, norm2=8.5348585e-13, result=0.0
🔍 Coarse search lag 29: correlation = 0.0
🔍 Coarse search lag 32: correlation = 0.0
🔍 Best correlation: 0.0 at period 0 (threshold: 0.6) [strategy: full]
❌ No pitch detected - best correlation 0.0 below threshold 0.6
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 91200)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0011747216, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 96000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 2.2512348e-09, Max: 6.085297e-07, Avg: 2.9576393e-08, Variance: 3.746288e-15
🔍 Performing autocorrelation search
🔍 Lag 26: corr=1.1711243e-13, norm1=9.035536e-13, norm2=9.142378e-13, result=0.0
🔍 Coarse search lag 26: correlation = 0.0
🔍 Lag 29: corr=1.464853e-13, norm1=9.02981e-13, norm2=9.134024e-13, result=0.0
🔍 Coarse search lag 29: correlation = 0.0
🔍 Coarse search lag 32: correlation = 0.0
🔍 Best correlation: 0.0 at period 0 (threshold: 0.6) [strategy: full]
❌ No pitch detected - best correlation 0.0 below threshold 0.6
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 96000)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0005570561, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 100800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 2.2512348e-09, Max: 1.5818962e-07, Avg: 1.8857708e-08, Variance: 2.6022882e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 100800)
� Calculated pressure: 0.0 cm H2O
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.0005672862, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 105600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 2.297597e-09, Max: 1.1305228e-07, Avg: 1.7265705e-08, Variance: 1.7381292e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 105600)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0006111232, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 110400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 3.2474348e-09, Max: 1.1305228e-07, Avg: 1.9100344e-08, Variance: 2.2319096e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 110400)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00238583, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 115200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 3.392884e-09, Max: 5.74668e-07, Avg: 2.100228e-08, Variance: 1.7258843e-15
🔍 Performing autocorrelation search
🔍 Lag 26: corr=1.11949593e-13, norm1=4.0797414e-13, norm2=4.2471715e-13, result=0.0
🔍 Coarse search lag 26: correlation = 0.0
🔍 Lag 29: corr=9.8239674e-14, norm1=4.063083e-13, norm2=4.1537737e-13, result=0.0
🔍 Coarse search lag 29: correlation = 0.0
🔍 Coarse search lag 32: correlation = 0.0
🔍 Best correlation: 0.0 at period 0 (threshold: 0.6) [strategy: full]
❌ No pitch detected - best correlation 0.0 below threshold 0.6
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 115200)
� Calculated pressure: 0.0 cm H2O
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.0007296904, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 120000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 3.392884e-09, Max: 5.74668e-07, Avg: 2.1708836e-08, Variance: 1.7431314e-15
🔍 Performing autocorrelation search
🔍 Lag 26: corr=1.1663123e-13, norm1=4.2646743e-13, norm2=4.320621e-13, result=0.0
🔍 Coarse search lag 26: correlation = 0.0
🔍 Lag 29: corr=1.08917405e-13, norm1=4.2592988e-13, norm2=4.3197008e-13, result=0.0
🔍 Coarse search lag 29: correlation = 0.0
🔍 Coarse search lag 32: correlation = 0.0
🔍 Best correlation: 0.0 at period 0 (threshold: 0.6) [strategy: full]
❌ No pitch detected - best correlation 0.0 below threshold 0.6
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 120000)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00091880385, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 124800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 4.3452415e-09, Max: 8.839389e-08, Avg: 2.4033262e-08, Variance: 2.8900277e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 124800)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0008219348, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 129600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 4.7322555e-09, Max: 1.2552431e-07, Avg: 2.5624166e-08, Variance: 3.2570873e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 129600)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0007022518, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 134400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 2.4448679e-09, Max: 1.2552431e-07, Avg: 2.077799e-08, Variance: 2.7228216e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 134400)
� Calculated pressure: 0.0 cm H2O
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.0016362162, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 139200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.3193538e-09, Max: 8.95822e-07, Avg: 1.9635374e-08, Variance: 4.4927796e-15
🔍 Performing autocorrelation search
🔍 Lag 26: corr=4.522152e-14, norm1=9.745056e-13, norm2=9.437003e-13, result=0.0
🔍 Coarse search lag 26: correlation = 0.0
🔍 Lag 29: corr=4.9828282e-14, norm1=9.74409e-13, norm2=9.42607e-13, result=0.0
🔍 Coarse search lag 29: correlation = 0.0
🔍 Coarse search lag 32: correlation = 0.0
🔍 Best correlation: 0.0 at period 0 (threshold: 0.6) [strategy: full]
❌ No pitch detected - best correlation 0.0 below threshold 0.6
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 139200)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00041302358, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 144000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.0579159e-09, Max: 8.95822e-07, Avg: 1.5321971e-08, Variance: 4.457785e-15
🔍 Performing autocorrelation search
🔍 Lag 26: corr=3.0050716e-14, norm1=9.350115e-13, norm2=9.369162e-13, result=0.0
🔍 Coarse search lag 26: correlation = 0.0
🔍 Lag 29: corr=3.289422e-14, norm1=9.349769e-13, norm2=9.366322e-13, result=0.0
🔍 Coarse search lag 29: correlation = 0.0
🔍 Coarse search lag 32: correlation = 0.0
🔍 Best correlation: 0.0 at period 0 (threshold: 0.6) [strategy: full]
❌ No pitch detected - best correlation 0.0 below threshold 0.6
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 144000)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.021664357, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 148800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.0579159e-09, Max: 0.00016181396, Avg: 7.866371e-06, Variance: 5.443083e-10
🔍 Performing autocorrelation search
🔍 Lag 26: corr=5.3071423e-09, norm1=1.17913416e-07, norm2=1.212376e-07, result=0.04438748
🔍 Coarse search lag 26: correlation = 0.04438748
🔍 Lag 29: corr=3.3476577e-09, norm1=1.114028e-07, norm2=1.212376e-07, result=0.028805427
🔍 Coarse search lag 29: correlation = 0.028805427
🔍 Coarse search lag 32: correlation = 0.019456215
🔍 Fine search around lag 26 in range [26, 28]
🔍 Lag 26: corr=5.3071423e-09, norm1=1.17913416e-07, norm2=1.212376e-07, result=0.04438748
🔍 Lag 27: corr=4.960082e-09, norm1=1.1690965e-07, norm2=1.212376e-07, result=0.04166247
🔍 Lag 28: corr=4.1147326e-09, norm1=1.1315931e-07, norm2=1.212376e-07, result=0.03512997
🔍 Best correlation: 0.04438748 at period 26 (threshold: 0.6) [strategy: full]
❌ No pitch detected - best correlation 0.04438748 below threshold 0.6
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 148800)
� Calculated pressure: 0.0 cm H2O
🫁 Breath detection: Starting new breath
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.029346507, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 153600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.1617354e-09, Max: 0.00026640057, Avg: 3.3426062e-05, Variance: 2.5199796e-09
🔍 Performing autocorrelation search
🔍 Lag 26: corr=4.9472096e-08, norm1=5.023861e-07, norm2=7.274562e-07, result=0.08183481
🔍 Coarse search lag 26: correlation = 0.08183481
🔍 Lag 29: corr=3.365616e-08, norm1=5.022739e-07, norm2=7.2745553e-07, result=0.05567895
🔍 Coarse search lag 29: correlation = 0.05567895
🔍 Coarse search lag 32: correlation = 0.040133554
🔍 Coarse search lag 53: correlation = 0.35393375
🔍 Coarse search lag 56: correlation = 0.43556532
🔍 Coarse search lag 59: correlation = 0.548032
🔍 Coarse search lag 62: correlation = 0.71537715
🔍 Coarse search lag 65: correlation = 0.77989215
🔍 Coarse search lag 68: correlation = 0.79618484
🔍 Coarse search lag 71: correlation = 0.7320121
🔍 Coarse search lag 74: correlation = 0.6720163
🔍 Coarse search lag 77: correlation = 0.5989748
🔍 Coarse search lag 80: correlation = 0.43858793
🔍 Coarse search lag 83: correlation = 0.34478438
🔍 Coarse search lag 122: correlation = 0.3283156
🔍 Coarse search lag 125: correlation = 0.44426912
🔍 Coarse search lag 128: correlation = 0.5461815
🔍 Coarse search lag 131: correlation = 0.8088652
🔍 Coarse search lag 134: correlation = 0.809899
🔍 Coarse search lag 137: correlation = 0.74882936
🔍 Coarse search lag 140: correlation = 0.67072666
🔍 Coarse search lag 143: correlation = 0.5371432
🔍 Coarse search lag 146: correlation = 0.48091304
🔍 Coarse search lag 149: correlation = 0.86108476
🔍 Coarse search lag 152: correlation = 0.6518483
🔍 Fine search around lag 149 in range [147, 151]
🔍 Fine search lag 147: correlation = 0.63783956
🔍 Fine search lag 148: correlation = 0.79823303
🔍 Fine search lag 149: correlation = 0.86108476
🔍 Fine search lag 150: correlation = 0.8624091
🔍 Fine search lag 151: correlation = 0.7486843
🔍 Best correlation: 0.8624091 at period 150 (threshold: 0.6) [strategy: full]
🎯 Target range updated: 7.0-8.888888 Hz (periods: 120-152)
📊 Moving averages updated - Period: 150.0, Amplitude: 0.8624091, RunLength: 1
✅ Pitch detected: 7.1111107 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.1111107 Hz
🎵 Detected pitch: 7.1111107 Hz (total samples: 153600)
� Calculated pressure: 3.2983327 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.1111107, Pressure: 3.2983327, Breaths: 0
🎤 Audio Level: 0.0319743, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 158400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 6.175148e-08, Max: 0.0002915436, Avg: 4.5227436e-05, Variance: 3.068106e-09
🎯 Economical search: one wavelength ahead 300, clamped 122, window [92, 152]
🎯 Economical lag 106: correlation = 0.3176702
🎯 Economical lag 107: correlation = 0.3622703
🎯 Economical lag 108: correlation = 0.44264174
🎯 Economical lag 109: correlation = 0.48866636
🎯 Economical lag 110: correlation = 0.50278604
🎯 Economical lag 111: correlation = 0.48097163
🎯 Economical lag 112: correlation = 0.5355984
🎯 Economical lag 113: correlation = 0.57934785
🎯 Economical lag 114: correlation = 0.5936684
🎯 Economical lag 115: correlation = 0.5996968
🎯 Economical lag 116: correlation = 0.6087288
🎯 Economical lag 117: correlation = 0.6286614
🎯 Economical lag 118: correlation = 0.6517059
🎯 Economical lag 119: correlation = 0.67298836
🎯 Economical lag 120: correlation = 0.67769206
🎯 Economical lag 121: correlation = 0.73069507
🎯 Economical lag 122: correlation = 0.77360785
🎯 Economical lag 123: correlation = 0.7751749
🎯 Economical lag 124: correlation = 0.7830808
🎯 Economical lag 125: correlation = 0.6853793
🎯 Economical lag 126: correlation = 0.63884157
🎯 Economical lag 127: correlation = 0.65605366
🎯 Economical lag 128: correlation = 0.67122275
🎯 Economical lag 129: correlation = 0.6409857
🎯 Economical lag 130: correlation = 0.5950821
🎯 Economical lag 131: correlation = 0.54134226
🎯 Economical lag 132: correlation = 0.4814911
🎯 Economical lag 133: correlation = 0.48811704
🎯 Economical lag 134: correlation = 0.5143894
🎯 Economical lag 135: correlation = 0.5431125
🎯 Economical lag 136: correlation = 0.5606745
🎯 Economical lag 137: correlation = 0.4980908
🎯 Economical lag 138: correlation = 0.47035873
🎯 Economical lag 139: correlation = 0.42613888
🎯 Economical lag 140: correlation = 0.40125602
🎯 Economical lag 141: correlation = 0.41111764
🎯 Economical lag 142: correlation = 0.38594836
🎯 Economical lag 143: correlation = 0.32633445
🔍 Best correlation: 0.7830808 at period 124 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-10.752687 Hz (periods: 99-152)
🔄 Run length reduced: 1 → 0 (leap: 1.4910393 Hz, threshold: 1.4222221 Hz)
🔄 Moving averages reset due to run length = 0
✅ Pitch detected: 8.60215 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 8.60215 Hz
🎵 Detected pitch: 8.60215 Hz (total samples: 158400)
� Calculated pressure: 4.966806 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 8.60215, Pressure: 4.966806, Breaths: 0
🎤 Audio Level: 0.04268633, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 163200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 2.9462115e-07, Max: 0.0004581754, Avg: 5.67871e-05, Variance: 7.2686293e-09
🎯 Economical search: one wavelength ahead 248, clamped 128, window [104, 152]
🎯 Economical lag 104: correlation = 0.5917336
🎯 Economical lag 105: correlation = 0.6350102
🎯 Economical lag 106: correlation = 0.61447513
🎯 Economical lag 107: correlation = 0.6040768
🎯 Economical lag 108: correlation = 0.69859624
🎯 Economical lag 109: correlation = 0.62310535
🎯 Economical lag 110: correlation = 0.57971007
🎯 Economical lag 111: correlation = 0.47088915
🎯 Economical lag 112: correlation = 0.44116527
🎯 Economical lag 113: correlation = 0.40950733
🎯 Economical lag 114: correlation = 0.4536963
🎯 Economical lag 115: correlation = 0.4565212
🎯 Economical lag 116: correlation = 0.44403586
🎯 Economical lag 117: correlation = 0.4378755
🎯 Economical lag 118: correlation = 0.4171081
🎯 Economical lag 119: correlation = 0.37750584
🎯 Economical lag 120: correlation = 0.3770553
🎯 Economical lag 121: correlation = 0.36642957
🎯 Economical lag 122: correlation = 0.36620525
🎯 Economical lag 123: correlation = 0.35728464
🎯 Economical lag 124: correlation = 0.33885047
🎯 Economical lag 151: correlation = 0.35193354
🎯 Economical lag 152: correlation = 0.39129457
🔍 Best correlation: 0.69859624 at period 108 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.4074073-12.345678 Hz (periods: 86-144)
📊 Moving averages updated - Period: 108.0, Amplitude: 0.69859624, RunLength: 1
✅ Pitch detected: 9.876543 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.876543 Hz
🎵 Detected pitch: 9.876543 Hz (total samples: 163200)
� Calculated pressure: 6.3928514 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.876543, Pressure: 6.3928514, Breaths: 0
🎤 Audio Level: 0.043375462, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 168000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 4.6869394e-07, Max: 0.00053896895, Avg: 8.377039e-05, Variance: 1.3660846e-08
🎯 Economical search: one wavelength ahead 216, clamped 131, window [110, 152]
🎯 Economical lag 110: correlation = 0.55246574
🎯 Economical lag 111: correlation = 0.44522125
🎯 Economical lag 112: correlation = 0.3203012
🎯 Economical lag 139: correlation = 0.32732153
🎯 Economical lag 140: correlation = 0.3882462
🎯 Economical lag 141: correlation = 0.49460816
🎯 Economical lag 142: correlation = 0.54017645
🎯 Economical lag 143: correlation = 0.5535921
🎯 Economical lag 144: correlation = 0.6058839
🎯 Economical lag 145: correlation = 0.63797617
🎯 Economical lag 146: correlation = 0.62312794
🎯 Economical lag 147: correlation = 0.58389443
🎯 Economical lag 148: correlation = 0.59793097
🎯 Economical lag 149: correlation = 0.6006067
🎯 Economical lag 150: correlation = 0.6369363
🎯 Economical lag 151: correlation = 0.74354345
🎯 Economical lag 152: correlation = 0.7968298
🔍 Best correlation: 0.7968298 at period 152 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-8.77193 Hz (periods: 121-152)
🔄 Run length reduced: 1 → 0 (leap: 2.8589993 Hz, threshold: 1.9753087 Hz)
🔄 Moving averages reset due to run length = 0
✅ Pitch detected: 7.017544 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.017544 Hz
🎵 Detected pitch: 7.017544 Hz (total samples: 168000)
� Calculated pressure: 3.1936312 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.017544, Pressure: 3.1936312, Breaths: 0
🎤 Audio Level: 0.046273276, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 172800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 6.2160245e-07, Max: 0.00053896895, Avg: 9.289394e-05, Variance: 1.4036134e-08
🎯 Economical search: one wavelength ahead 304, clamped 122, window [92, 152]
🎯 Economical lag 92: correlation = 0.40471593
🎯 Economical lag 93: correlation = 0.40857115
🎯 Economical lag 94: correlation = 0.4369758
🎯 Economical lag 95: correlation = 0.4494462
🎯 Economical lag 96: correlation = 0.4793087
🎯 Economical lag 97: correlation = 0.5345231
🎯 Economical lag 98: correlation = 0.6007181
🎯 Economical lag 99: correlation = 0.6060263
🎯 Economical lag 100: correlation = 0.6374291
🎯 Economical lag 101: correlation = 0.71092695
🎯 Economical lag 102: correlation = 0.70912933
🎯 Economical lag 103: correlation = 0.70855254
🎯 Economical lag 104: correlation = 0.7356907
🎯 Economical lag 105: correlation = 0.7048403
🎯 Economical lag 106: correlation = 0.70545
🎯 Economical lag 107: correlation = 0.72122926
🎯 Economical lag 108: correlation = 0.68578166
🎯 Economical lag 109: correlation = 0.66548437
🎯 Economical lag 110: correlation = 0.66839355
🎯 Economical lag 111: correlation = 0.670604
🎯 Economical lag 112: correlation = 0.6239823
🎯 Economical lag 113: correlation = 0.5515167
🎯 Economical lag 114: correlation = 0.47479373
🎯 Economical lag 115: correlation = 0.39547986
🎯 Economical lag 116: correlation = 0.3164348
🎯 Economical lag 136: correlation = 0.30130413
🎯 Economical lag 137: correlation = 0.3207353
🎯 Economical lag 138: correlation = 0.32056454
🎯 Economical lag 139: correlation = 0.3339071
🎯 Economical lag 140: correlation = 0.36724254
🎯 Economical lag 141: correlation = 0.43703818
🎯 Economical lag 142: correlation = 0.47195655
🎯 Economical lag 143: correlation = 0.511574
🎯 Economical lag 144: correlation = 0.5626058
🎯 Economical lag 145: correlation = 0.6307399
🎯 Economical lag 146: correlation = 0.6579227
🎯 Economical lag 147: correlation = 0.6725704
🎯 Economical lag 148: correlation = 0.7095925
🎯 Economical lag 149: correlation = 0.7301867
🎯 Economical lag 150: correlation = 0.7450799
🎯 Economical lag 151: correlation = 0.7645871
🎯 Economical lag 152: correlation = 0.7564512
🔍 Best correlation: 0.7645871 at period 151 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-8.830022 Hz (periods: 120-152)
📊 Moving averages updated - Period: 151.0, Amplitude: 0.7645871, RunLength: 1
✅ Pitch detected: 7.0640173 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.0640173 Hz
🎵 Detected pitch: 7.0640173 Hz (total samples: 172800)
� Calculated pressure: 3.245635 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.0640173, Pressure: 3.245635, Breaths: 0
🎤 Audio Level: 0.04296179, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 177600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 5.667401e-07, Max: 0.00046827862, Avg: 8.305327e-05, Variance: 9.8903365e-09
🎯 Economical search: one wavelength ahead 302, clamped 122, window [92, 152]
🎯 Economical lag 92: correlation = 0.5443795
🎯 Economical lag 93: correlation = 0.54894996
🎯 Economical lag 94: correlation = 0.58174133
🎯 Economical lag 95: correlation = 0.61759186
🎯 Economical lag 96: correlation = 0.66899586
🎯 Economical lag 97: correlation = 0.64835685
🎯 Economical lag 98: correlation = 0.6729802
🎯 Economical lag 99: correlation = 0.7274644
🎯 Economical lag 100: correlation = 0.75518996
🎯 Economical lag 101: correlation = 0.73830855
🎯 Economical lag 102: correlation = 0.71082854
🎯 Economical lag 103: correlation = 0.6906827
🎯 Economical lag 104: correlation = 0.6313851
🎯 Economical lag 105: correlation = 0.60153157
🎯 Economical lag 106: correlation = 0.5780723
🎯 Economical lag 107: correlation = 0.55818725
🎯 Economical lag 108: correlation = 0.55966514
🎯 Economical lag 109: correlation = 0.551744
🎯 Economical lag 110: correlation = 0.5354803
🎯 Economical lag 111: correlation = 0.5132192
🎯 Economical lag 112: correlation = 0.437965
🎯 Economical lag 113: correlation = 0.38770238
🎯 Economical lag 114: correlation = 0.3510492
🎯 Economical lag 115: correlation = 0.3111562
🎯 Economical lag 139: correlation = 0.30128077
🎯 Economical lag 140: correlation = 0.3784148
🎯 Economical lag 141: correlation = 0.44278127
🎯 Economical lag 142: correlation = 0.5201141
🎯 Economical lag 143: correlation = 0.6096229
🎯 Economical lag 144: correlation = 0.63688654
🎯 Economical lag 145: correlation = 0.663609
🎯 Economical lag 146: correlation = 0.6914081
🎯 Economical lag 147: correlation = 0.7666286
🎯 Economical lag 148: correlation = 0.77078414
🎯 Economical lag 149: correlation = 0.7803483
🎯 Economical lag 150: correlation = 0.8253183
🎯 Economical lag 151: correlation = 0.81222767
🎯 Economical lag 152: correlation = 0.81722945
🔍 Best correlation: 0.8253183 at period 150 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-8.888888 Hz (periods: 120-152)
📊 Moving averages updated - Period: 150.5, Amplitude: 0.7949527, RunLength: 2
✅ Pitch detected: 7.1111107 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.1111107 Hz
🎵 Detected pitch: 7.1111107 Hz (total samples: 177600)
� Calculated pressure: 3.2983327 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.1111107, Pressure: 3.2983327, Breaths: 0
🎤 Audio Level: 0.040527314, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 182400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 5.667401e-07, Max: 0.00062364567, Avg: 6.585732e-05, Variance: 7.951459e-09
🎯 Economical search: one wavelength ahead 300, clamped 122, window [92, 152]
🎯 Economical lag 92: correlation = 0.47085735
🎯 Economical lag 93: correlation = 0.48024786
🎯 Economical lag 94: correlation = 0.46339193
🎯 Economical lag 95: correlation = 0.49300382
🎯 Economical lag 96: correlation = 0.5455311
🎯 Economical lag 97: correlation = 0.59462386
🎯 Economical lag 98: correlation = 0.6193799
🎯 Economical lag 99: correlation = 0.6559059
🎯 Economical lag 100: correlation = 0.72022617
🎯 Economical lag 101: correlation = 0.6899198
🎯 Economical lag 102: correlation = 0.7126585
🎯 Economical lag 103: correlation = 0.7213628
🎯 Economical lag 104: correlation = 0.6845806
🎯 Economical lag 105: correlation = 0.70131755
🎯 Economical lag 106: correlation = 0.6990187
🎯 Economical lag 107: correlation = 0.74681395
🎯 Economical lag 108: correlation = 0.67123365
🎯 Economical lag 109: correlation = 0.5684848
🎯 Economical lag 110: correlation = 0.4651375
🎯 Economical lag 111: correlation = 0.39798015
🎯 Economical lag 112: correlation = 0.39676052
🎯 Economical lag 113: correlation = 0.3331954
🎯 Economical lag 138: correlation = 0.36803597
🎯 Economical lag 139: correlation = 0.37772495
🎯 Economical lag 140: correlation = 0.33470422
🎯 Economical lag 141: correlation = 0.3493353
🎯 Economical lag 142: correlation = 0.35957214
🎯 Economical lag 143: correlation = 0.38105914
🎯 Economical lag 144: correlation = 0.32226655
🎯 Economical lag 147: correlation = 0.34538084
🎯 Economical lag 148: correlation = 0.45318973
🎯 Economical lag 149: correlation = 0.6283851
🎯 Economical lag 150: correlation = 0.71361744
🎯 Economical lag 151: correlation = 0.7010234
🎯 Economical lag 152: correlation = 0.6703194
🔍 Best correlation: 0.74681395 at period 107 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.4766355-12.46106 Hz (periods: 85-142)
🔄 Run length reduced: 2 → 0 (leap: 2.8813615 Hz, threshold: 1.4174972 Hz)
🔄 Moving averages reset due to run length = 0
✅ Pitch detected: 9.968847 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.968847 Hz
🎵 Detected pitch: 9.968847 Hz (total samples: 182400)
� Calculated pressure: 6.49614 cm H2O
🖥️ UI updated - Freq: 9.968847, Pressure: 6.49614, Breaths: 0
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.0359853, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 187200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 7.091324e-07, Max: 0.00062364567, Avg: 6.531266e-05, Variance: 7.0161734e-09
🎯 Economical search: one wavelength ahead 214, clamped 131, window [110, 152]
🎯 Economical lag 110: correlation = 0.5813206
🎯 Economical lag 111: correlation = 0.562594
🎯 Economical lag 112: correlation = 0.5466103
🎯 Economical lag 113: correlation = 0.49346212
🎯 Economical lag 114: correlation = 0.4293131
🎯 Economical lag 115: correlation = 0.36894277
🎯 Economical lag 116: correlation = 0.36519065
🎯 Economical lag 117: correlation = 0.36083642
🎯 Economical lag 118: correlation = 0.30817777
🎯 Economical lag 139: correlation = 0.30978766
🎯 Economical lag 140: correlation = 0.34655225
🎯 Economical lag 141: correlation = 0.4363072
🎯 Economical lag 142: correlation = 0.4823296
🎯 Economical lag 143: correlation = 0.5372672
🎯 Economical lag 144: correlation = 0.5750324
🎯 Economical lag 145: correlation = 0.5964962
🎯 Economical lag 146: correlation = 0.64781344
🎯 Economical lag 147: correlation = 0.6646748
🎯 Economical lag 148: correlation = 0.68366313
🎯 Economical lag 149: correlation = 0.6745247
🎯 Economical lag 150: correlation = 0.69080174
🎯 Economical lag 151: correlation = 0.8013427
🎯 Economical lag 152: correlation = 0.75457865
🔍 Best correlation: 0.8013427 at period 151 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-8.830022 Hz (periods: 120-152)
📊 Moving averages updated - Period: 151.0, Amplitude: 0.8013427, RunLength: 1
✅ Pitch detected: 7.0640173 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.0640173 Hz
🎵 Detected pitch: 7.0640173 Hz (total samples: 187200)
� Calculated pressure: 3.245635 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.0640173, Pressure: 3.245635, Breaths: 0
🎤 Audio Level: 0.035779748, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 192000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 6.6776335e-07, Max: 0.00042644583, Avg: 7.29887e-05, Variance: 5.9860383e-09
🎯 Economical search: one wavelength ahead 302, clamped 122, window [92, 152]
🎯 Economical lag 92: correlation = 0.597228
🎯 Economical lag 93: correlation = 0.6441385
🎯 Economical lag 94: correlation = 0.68179715
🎯 Economical lag 95: correlation = 0.70970935
🎯 Economical lag 96: correlation = 0.74589306
🎯 Economical lag 97: correlation = 0.74381715
🎯 Economical lag 98: correlation = 0.7143436
🎯 Economical lag 99: correlation = 0.7531141
🎯 Economical lag 100: correlation = 0.76656336
🎯 Economical lag 101: correlation = 0.7568143
🎯 Economical lag 102: correlation = 0.7507232
🎯 Economical lag 103: correlation = 0.7041063
🎯 Economical lag 104: correlation = 0.6810344
🎯 Economical lag 105: correlation = 0.6952437
🎯 Economical lag 106: correlation = 0.70584977
🎯 Economical lag 107: correlation = 0.6775316
🎯 Economical lag 108: correlation = 0.6089734
🎯 Economical lag 109: correlation = 0.5552932
🎯 Economical lag 110: correlation = 0.5152755
🎯 Economical lag 111: correlation = 0.49944133
🎯 Economical lag 112: correlation = 0.47864214
🎯 Economical lag 113: correlation = 0.4426964
🎯 Economical lag 114: correlation = 0.42573836
🎯 Economical lag 115: correlation = 0.41117963
🎯 Economical lag 116: correlation = 0.3617391
🎯 Economical lag 117: correlation = 0.32064757
🎯 Economical lag 118: correlation = 0.32457158
🎯 Economical lag 119: correlation = 0.3372444
🎯 Economical lag 120: correlation = 0.30044287
🎯 Economical lag 134: correlation = 0.30803812
🎯 Economical lag 135: correlation = 0.3073062
🎯 Economical lag 136: correlation = 0.32657734
🎯 Economical lag 137: correlation = 0.36186853
🎯 Economical lag 138: correlation = 0.429469
🎯 Economical lag 139: correlation = 0.4899715
🎯 Economical lag 140: correlation = 0.5113095
🎯 Economical lag 141: correlation = 0.54219955
🎯 Economical lag 142: correlation = 0.52052647
🎯 Economical lag 143: correlation = 0.55416566
🎯 Economical lag 144: correlation = 0.5698826
🎯 Economical lag 145: correlation = 0.58389074
🎯 Economical lag 146: correlation = 0.61206526
🎯 Economical lag 147: correlation = 0.6551433
🎯 Economical lag 148: correlation = 0.6815441
🎯 Economical lag 149: correlation = 0.6645915
🎯 Economical lag 150: correlation = 0.73732203
🎯 Economical lag 151: correlation = 0.8462905
🎯 Economical lag 152: correlation = 0.8364153
🔍 Best correlation: 0.8462905 at period 151 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-8.830022 Hz (periods: 120-152)
📊 Moving averages updated - Period: 151.0, Amplitude: 0.82381666, RunLength: 2
✅ Pitch detected: 7.0640173 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.0640173 Hz
🎵 Detected pitch: 7.0640173 Hz (total samples: 192000)
� Calculated pressure: 3.245635 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.0640173, Pressure: 3.245635, Breaths: 0
🎤 Audio Level: 0.03800461, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 196800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 4.4977713e-07, Max: 0.00035459409, Avg: 7.264175e-05, Variance: 5.849249e-09
🎯 Economical search: one wavelength ahead 302, clamped 122, window [92, 152]
🎯 Economical lag 92: correlation = 0.47333568
🎯 Economical lag 93: correlation = 0.52141935
🎯 Economical lag 94: correlation = 0.524209
🎯 Economical lag 95: correlation = 0.5481728
🎯 Economical lag 96: correlation = 0.58376026
🎯 Economical lag 97: correlation = 0.61706674
🎯 Economical lag 98: correlation = 0.656209
🎯 Economical lag 99: correlation = 0.68497616
🎯 Economical lag 100: correlation = 0.74610966
🎯 Economical lag 101: correlation = 0.76719445
🎯 Economical lag 102: correlation = 0.78865
🎯 Economical lag 103: correlation = 0.8264097
🎯 Economical lag 104: correlation = 0.8620528
🎯 Economical lag 105: correlation = 0.8873681
🎯 Economical lag 106: correlation = 0.8434569
🎯 Economical lag 107: correlation = 0.76588136
🎯 Economical lag 108: correlation = 0.71113807
🎯 Economical lag 109: correlation = 0.68126726
🎯 Economical lag 110: correlation = 0.67811835
🎯 Economical lag 111: correlation = 0.6650044
🎯 Economical lag 112: correlation = 0.60165805
🎯 Economical lag 113: correlation = 0.5454362
🎯 Economical lag 114: correlation = 0.47904179
🎯 Economical lag 115: correlation = 0.4676426
🎯 Economical lag 116: correlation = 0.42493716
🎯 Economical lag 117: correlation = 0.40066728
🎯 Economical lag 118: correlation = 0.3648918
🎯 Economical lag 119: correlation = 0.3206249
🎯 Economical lag 122: correlation = 0.30182943
🎯 Economical lag 123: correlation = 0.30081066
🎯 Economical lag 130: correlation = 0.30061507
🎯 Economical lag 131: correlation = 0.31012595
🎯 Economical lag 132: correlation = 0.32842714
🎯 Economical lag 133: correlation = 0.32784697
🎯 Economical lag 134: correlation = 0.33539534
🎯 Economical lag 135: correlation = 0.35691607
🎯 Economical lag 136: correlation = 0.3782451
🎯 Economical lag 137: correlation = 0.41174883
🎯 Economical lag 138: correlation = 0.4161201
🎯 Economical lag 139: correlation = 0.41780347
🎯 Economical lag 140: correlation = 0.44318587
🎯 Economical lag 141: correlation = 0.46099183
🎯 Economical lag 142: correlation = 0.4767518
🎯 Economical lag 143: correlation = 0.48296127
🎯 Economical lag 144: correlation = 0.5069285
🎯 Economical lag 145: correlation = 0.5280636
🎯 Economical lag 146: correlation = 0.51711416
🎯 Economical lag 147: correlation = 0.5659425
🎯 Economical lag 148: correlation = 0.61918503
🎯 Economical lag 149: correlation = 0.6739572
🎯 Economical lag 150: correlation = 0.6950851
🎯 Economical lag 151: correlation = 0.7419794
🎯 Economical lag 152: correlation = 0.76124376
🔍 Best correlation: 0.8873681 at period 105 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.619047-12.698412 Hz (periods: 84-140)
🔄 Run length reduced: 2 → 0 (leap: 3.0947123 Hz, threshold: 1.4128035 Hz)
🔄 Moving averages reset due to run length = 0
✅ Pitch detected: 10.15873 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 10.15873 Hz
🎵 Detected pitch: 10.15873 Hz (total samples: 196800)
� Calculated pressure: 6.7086177 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 10.15873, Pressure: 6.7086177, Breaths: 0
🎤 Audio Level: 0.04213317, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 201600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 9.812053e-07, Max: 0.00032672682, Avg: 6.803365e-05, Variance: 5.8193974e-09
🎯 Economical search: one wavelength ahead 210, clamped 131, window [110, 152]
🎯 Economical lag 110: correlation = 0.6813815
🎯 Economical lag 111: correlation = 0.6253515
🎯 Economical lag 112: correlation = 0.60011685
🎯 Economical lag 113: correlation = 0.60224026
🎯 Economical lag 114: correlation = 0.59475666
🎯 Economical lag 115: correlation = 0.5714489
🎯 Economical lag 116: correlation = 0.52381206
🎯 Economical lag 117: correlation = 0.48060593
🎯 Economical lag 118: correlation = 0.46458223
🎯 Economical lag 119: correlation = 0.4379458
🎯 Economical lag 120: correlation = 0.4428962
🎯 Economical lag 121: correlation = 0.3987048
🎯 Economical lag 122: correlation = 0.3442414
🎯 Economical lag 137: correlation = 0.30730066
🎯 Economical lag 138: correlation = 0.32003233
🎯 Economical lag 139: correlation = 0.32473192
🎯 Economical lag 140: correlation = 0.350695
🎯 Economical lag 141: correlation = 0.38122404
🎯 Economical lag 142: correlation = 0.3839118
🎯 Economical lag 143: correlation = 0.39457613
🎯 Economical lag 144: correlation = 0.4242181
🎯 Economical lag 145: correlation = 0.46997818
🎯 Economical lag 146: correlation = 0.49837926
🎯 Economical lag 147: correlation = 0.565873
🎯 Economical lag 148: correlation = 0.6518134
🎯 Economical lag 149: correlation = 0.65355587
🎯 Economical lag 150: correlation = 0.5834322
🎯 Economical lag 151: correlation = 0.5848938
🎯 Economical lag 152: correlation = 0.6736236
🔍 Best correlation: 0.6813815 at period 110 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.272727-12.121211 Hz (periods: 88-146)
📊 Moving averages updated - Period: 110.0, Amplitude: 0.6813815, RunLength: 1
✅ Pitch detected: 9.696969 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.696969 Hz
🎵 Detected pitch: 9.696969 Hz (total samples: 201600)
� Calculated pressure: 6.1919084 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.696969, Pressure: 6.1919084, Breaths: 0
🎤 Audio Level: 0.04532569, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 206400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 5.657557e-07, Max: 0.0005908896, Avg: 7.78937e-05, Variance: 9.690645e-09
🎯 Economical search: one wavelength ahead 220, clamped 130, window [108, 152]
🎯 Economical lag 108: correlation = 0.5863236
🎯 Economical lag 109: correlation = 0.54047287
🎯 Economical lag 110: correlation = 0.54093415
🎯 Economical lag 111: correlation = 0.55966026
🎯 Economical lag 112: correlation = 0.53289604
🎯 Economical lag 113: correlation = 0.46597704
🎯 Economical lag 114: correlation = 0.44208705
🎯 Economical lag 115: correlation = 0.4150668
🎯 Economical lag 116: correlation = 0.363283
🎯 Economical lag 117: correlation = 0.31097785
🎯 Economical lag 139: correlation = 0.36261955
🎯 Economical lag 140: correlation = 0.42319772
🎯 Economical lag 141: correlation = 0.4705314
🎯 Economical lag 142: correlation = 0.53277737
🎯 Economical lag 143: correlation = 0.4691128
🎯 Economical lag 144: correlation = 0.41547436
🎯 Economical lag 145: correlation = 0.40146074
🎯 Economical lag 146: correlation = 0.39811346
🎯 Economical lag 147: correlation = 0.45870677
🎯 Economical lag 148: correlation = 0.43041718
🎯 Economical lag 149: correlation = 0.46517837
🎯 Economical lag 150: correlation = 0.5615615
🎯 Economical lag 151: correlation = 0.6010806
🎯 Economical lag 152: correlation = 0.63132864
🔍 Best correlation: 0.63132864 at period 152 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-8.77193 Hz (periods: 121-152)
🔄 Run length reduced: 1 → 0 (leap: 2.6794252 Hz, threshold: 1.9393939 Hz)
🔄 Moving averages reset due to run length = 0
✅ Pitch detected: 7.017544 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.017544 Hz
🎵 Detected pitch: 7.017544 Hz (total samples: 206400)
� Calculated pressure: 3.1936312 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.017544, Pressure: 3.1936312, Breaths: 0
🎤 Audio Level: 0.03305057, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 211200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 4.1466632e-07, Max: 0.0005908896, Avg: 7.452495e-05, Variance: 8.968106e-09
🎯 Economical search: one wavelength ahead 304, clamped 122, window [92, 152]
🎯 Economical lag 93: correlation = 0.33897892
🎯 Economical lag 94: correlation = 0.41048875
🎯 Economical lag 95: correlation = 0.47475097
🎯 Economical lag 96: correlation = 0.47851682
🎯 Economical lag 97: correlation = 0.4967041
🎯 Economical lag 98: correlation = 0.513796
🎯 Economical lag 99: correlation = 0.55654436
🎯 Economical lag 100: correlation = 0.59757537
🎯 Economical lag 101: correlation = 0.6032935
🎯 Economical lag 102: correlation = 0.6513244
🎯 Economical lag 103: correlation = 0.70938253
🎯 Economical lag 104: correlation = 0.73970705
🎯 Economical lag 105: correlation = 0.7198062
🎯 Economical lag 106: correlation = 0.69530284
🎯 Economical lag 107: correlation = 0.6834155
🎯 Economical lag 108: correlation = 0.70715076
🎯 Economical lag 109: correlation = 0.7484825
🎯 Economical lag 110: correlation = 0.719613
🎯 Economical lag 111: correlation = 0.6946534
🎯 Economical lag 112: correlation = 0.64091176
🎯 Economical lag 113: correlation = 0.63810533
🎯 Economical lag 114: correlation = 0.66668636
🎯 Economical lag 115: correlation = 0.64871347
🎯 Economical lag 116: correlation = 0.54583246
🎯 Economical lag 117: correlation = 0.48449197
🎯 Economical lag 118: correlation = 0.41333067
🎯 Economical lag 119: correlation = 0.34317642
🎯 Economical lag 120: correlation = 0.32291886
🎯 Economical lag 142: correlation = 0.3015254
🎯 Economical lag 143: correlation = 0.34886968
🎯 Economical lag 144: correlation = 0.4056767
🎯 Economical lag 145: correlation = 0.4421491
🎯 Economical lag 146: correlation = 0.43935132
🎯 Economical lag 147: correlation = 0.48896608
🎯 Economical lag 148: correlation = 0.5925215
🎯 Economical lag 149: correlation = 0.6123866
🎯 Economical lag 150: correlation = 0.59071827
🎯 Economical lag 151: correlation = 0.6217089
🎯 Economical lag 152: correlation = 0.6576325
🔍 Best correlation: 0.7484825 at period 109 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.3394494-12.232416 Hz (periods: 87-145)
📊 Moving averages updated - Period: 109.0, Amplitude: 0.7484825, RunLength: 1
✅ Pitch detected: 9.785933 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.785933 Hz
🎵 Detected pitch: 9.785933 Hz (total samples: 211200)
� Calculated pressure: 6.2914586 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.785933, Pressure: 6.2914586, Breaths: 0
🎤 Audio Level: 0.0434423, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 216000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 3.61026e-07, Max: 0.0006772473, Avg: 6.744891e-05, Variance: 9.03116e-09
🎯 Economical search: one wavelength ahead 218, clamped 131, window [110, 152]
🎯 Economical lag 110: correlation = 0.4828961
🎯 Economical lag 111: correlation = 0.5175325
🎯 Economical lag 112: correlation = 0.48631603
🎯 Economical lag 113: correlation = 0.4328173
🎯 Economical lag 114: correlation = 0.3922099
🎯 Economical lag 115: correlation = 0.33495775
🎯 Economical lag 143: correlation = 0.37390274
🎯 Economical lag 144: correlation = 0.4845158
🎯 Economical lag 145: correlation = 0.6647318
🎯 Economical lag 146: correlation = 0.7351002
🎯 Economical lag 147: correlation = 0.81061286
🎯 Economical lag 148: correlation = 0.81168085
🎯 Economical lag 149: correlation = 0.7471976
🎯 Economical lag 150: correlation = 0.7265032
🎯 Economical lag 151: correlation = 0.7103263
🎯 Economical lag 152: correlation = 0.69479007
🔍 Best correlation: 0.81168085 at period 148 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-9.009008 Hz (periods: 118-152)
🔄 Run length reduced: 1 → 0 (leap: 2.5787258 Hz, threshold: 1.9571866 Hz)
🔄 Moving averages reset due to run length = 0
✅ Pitch detected: 7.2072067 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.2072067 Hz
🎵 Detected pitch: 7.2072067 Hz (total samples: 216000)
� Calculated pressure: 3.4058642 cm H2O
🖥️ UI updated - Freq: 7.2072067, Pressure: 3.4058642, Breaths: 0
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.03095951, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 220800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.8113658e-07, Max: 0.0006772473, Avg: 6.248211e-05, Variance: 8.469681e-09
🎯 Economical search: one wavelength ahead 296, clamped 123, window [94, 152]
🎯 Economical lag 94: correlation = 0.39533958
🎯 Economical lag 95: correlation = 0.49024737
🎯 Economical lag 96: correlation = 0.5986457
🎯 Economical lag 97: correlation = 0.65124446
🎯 Economical lag 98: correlation = 0.72265923
🎯 Economical lag 99: correlation = 0.72664046
🎯 Economical lag 100: correlation = 0.7053469
🎯 Economical lag 101: correlation = 0.7244204
🎯 Economical lag 102: correlation = 0.71491426
🎯 Economical lag 103: correlation = 0.74277395
🎯 Economical lag 104: correlation = 0.7464987
🎯 Economical lag 105: correlation = 0.7312921
🎯 Economical lag 106: correlation = 0.72670203
🎯 Economical lag 107: correlation = 0.62422407
🎯 Economical lag 108: correlation = 0.5603784
🎯 Economical lag 109: correlation = 0.53132117
🎯 Economical lag 110: correlation = 0.4733505
🎯 Economical lag 111: correlation = 0.46661046
🎯 Economical lag 112: correlation = 0.47101852
🎯 Economical lag 113: correlation = 0.4424046
🎯 Economical lag 114: correlation = 0.43851063
🎯 Economical lag 115: correlation = 0.39765602
🎯 Economical lag 116: correlation = 0.39851278
🎯 Economical lag 117: correlation = 0.3425211
🎯 Economical lag 143: correlation = 0.3097715
🎯 Economical lag 144: correlation = 0.34564373
🎯 Economical lag 145: correlation = 0.38215834
🎯 Economical lag 146: correlation = 0.41373867
🎯 Economical lag 147: correlation = 0.47464588
🎯 Economical lag 148: correlation = 0.5237024
🎯 Economical lag 149: correlation = 0.59263223
🎯 Economical lag 150: correlation = 0.69160193
🎯 Economical lag 151: correlation = 0.7363115
🎯 Economical lag 152: correlation = 0.7884732
🔍 Best correlation: 0.7884732 at period 152 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-8.77193 Hz (periods: 121-152)
📊 Moving averages updated - Period: 152.0, Amplitude: 0.7884732, RunLength: 1
✅ Pitch detected: 7.017544 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.017544 Hz
🎵 Detected pitch: 7.017544 Hz (total samples: 220800)
� Calculated pressure: 3.1936312 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🫁 Breath detection: Breath now active (duration: 1.5s)
🖥️ UI updated - Freq: 7.017544, Pressure: 3.1936312, Breaths: 0
🎤 Audio Level: 0.042860195, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 225600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 3.1304657e-07, Max: 0.0005374466, Avg: 6.420876e-05, Variance: 7.97227e-09
🎯 Economical search: one wavelength ahead 304, clamped 122, window [92, 152]
🎯 Economical lag 92: correlation = 0.42634735
🎯 Economical lag 93: correlation = 0.46232292
🎯 Economical lag 94: correlation = 0.5039828
🎯 Economical lag 95: correlation = 0.47353688
🎯 Economical lag 96: correlation = 0.46369636
🎯 Economical lag 97: correlation = 0.5281128
🎯 Economical lag 98: correlation = 0.59372747
🎯 Economical lag 99: correlation = 0.6513714
🎯 Economical lag 100: correlation = 0.742164
🎯 Economical lag 101: correlation = 0.7519862
🎯 Economical lag 102: correlation = 0.75154924
🎯 Economical lag 103: correlation = 0.73613316
🎯 Economical lag 104: correlation = 0.68263793
🎯 Economical lag 105: correlation = 0.6999328
🎯 Economical lag 106: correlation = 0.65980655
🎯 Economical lag 107: correlation = 0.59069204
🎯 Economical lag 108: correlation = 0.56495464
🎯 Economical lag 109: correlation = 0.52348405
🎯 Economical lag 110: correlation = 0.49171153
🎯 Economical lag 111: correlation = 0.4869221
🎯 Economical lag 112: correlation = 0.50998133
🎯 Economical lag 113: correlation = 0.49638087
🎯 Economical lag 114: correlation = 0.44895884
🎯 Economical lag 115: correlation = 0.4319795
🎯 Economical lag 116: correlation = 0.43711278
🎯 Economical lag 117: correlation = 0.40235674
🎯 Economical lag 118: correlation = 0.3954839
🎯 Economical lag 119: correlation = 0.33632416
🎯 Economical lag 144: correlation = 0.38173547
🎯 Economical lag 145: correlation = 0.39921618
🎯 Economical lag 146: correlation = 0.46330926
🎯 Economical lag 147: correlation = 0.5468182
🎯 Economical lag 148: correlation = 0.6276493
🎯 Economical lag 149: correlation = 0.6886386
🎯 Economical lag 150: correlation = 0.7470249
🎯 Economical lag 151: correlation = 0.81909245
🎯 Economical lag 152: correlation = 0.8529117
🔍 Best correlation: 0.8529117 at period 152 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-8.77193 Hz (periods: 121-152)
📊 Moving averages updated - Period: 152.0, Amplitude: 0.8206924, RunLength: 2
✅ Pitch detected: 7.017544 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.017544 Hz
🎵 Detected pitch: 7.017544 Hz (total samples: 225600)
� Calculated pressure: 3.1936312 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.017544, Pressure: 3.1936312, Breaths: 0
🎤 Audio Level: 0.039563365, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 230400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 3.1304657e-07, Max: 0.0005374466, Avg: 6.123376e-05, Variance: 8.610356e-09
🎯 Economical search: one wavelength ahead 304, clamped 122, window [92, 152]
🎯 Economical lag 92: correlation = 0.37993762
🎯 Economical lag 93: correlation = 0.44749716
🎯 Economical lag 94: correlation = 0.46812883
🎯 Economical lag 95: correlation = 0.45716295
🎯 Economical lag 96: correlation = 0.44125262
🎯 Economical lag 97: correlation = 0.46309376
🎯 Economical lag 98: correlation = 0.5214965
🎯 Economical lag 99: correlation = 0.574009
🎯 Economical lag 100: correlation = 0.5540395
🎯 Economical lag 101: correlation = 0.5832821
🎯 Economical lag 102: correlation = 0.5909572
🎯 Economical lag 103: correlation = 0.604864
🎯 Economical lag 104: correlation = 0.6536385
🎯 Economical lag 105: correlation = 0.63999164
🎯 Economical lag 106: correlation = 0.63141173
🎯 Economical lag 107: correlation = 0.60661566
🎯 Economical lag 108: correlation = 0.579581
🎯 Economical lag 109: correlation = 0.56564057
🎯 Economical lag 110: correlation = 0.55225855
🎯 Economical lag 111: correlation = 0.50057745
🎯 Economical lag 112: correlation = 0.45750964
🎯 Economical lag 113: correlation = 0.37639913
🎯 Economical lag 145: correlation = 0.32036078
🎯 Economical lag 146: correlation = 0.36168933
🎯 Economical lag 147: correlation = 0.38615894
🎯 Economical lag 148: correlation = 0.5043633
🎯 Economical lag 149: correlation = 0.6545148
🎯 Economical lag 150: correlation = 0.69280493
🎯 Economical lag 151: correlation = 0.7245465
🎯 Economical lag 152: correlation = 0.76654655
🔍 Best correlation: 0.76654655 at period 152 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-8.77193 Hz (periods: 121-152)
📊 Moving averages updated - Period: 152.0, Amplitude: 0.8026438, RunLength: 3
✅ Pitch detected: 7.017544 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.017544 Hz
🎵 Detected pitch: 7.017544 Hz (total samples: 230400)
� Calculated pressure: 3.1936312 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.017544, Pressure: 3.1936312, Breaths: 0
🎤 Audio Level: 0.038414843, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 235200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 8.036608e-07, Max: 0.00037115664, Avg: 5.3625834e-05, Variance: 4.932554e-09
🎯 Economical search: one wavelength ahead 304, clamped 122, window [92, 152]
🎯 Economical lag 92: correlation = 0.53550017
🎯 Economical lag 93: correlation = 0.5864658
🎯 Economical lag 94: correlation = 0.6210995
🎯 Economical lag 95: correlation = 0.6692568
🎯 Economical lag 96: correlation = 0.70120174
🎯 Economical lag 97: correlation = 0.72160834
🎯 Economical lag 98: correlation = 0.74189276
🎯 Economical lag 99: correlation = 0.69676244
🎯 Economical lag 100: correlation = 0.6972415
🎯 Economical lag 101: correlation = 0.7559672
🎯 Economical lag 102: correlation = 0.7750522
🎯 Economical lag 103: correlation = 0.73699856
🎯 Economical lag 104: correlation = 0.71855605
🎯 Economical lag 105: correlation = 0.7422154
🎯 Economical lag 106: correlation = 0.7351939
🎯 Economical lag 107: correlation = 0.72803545
🎯 Economical lag 108: correlation = 0.62718636
🎯 Economical lag 109: correlation = 0.59357053
🎯 Economical lag 110: correlation = 0.5616331
🎯 Economical lag 111: correlation = 0.48920465
🎯 Economical lag 112: correlation = 0.45632902
🎯 Economical lag 113: correlation = 0.3694709
🎯 Economical lag 114: correlation = 0.31805092
🎯 Economical lag 139: correlation = 0.3055542
🎯 Economical lag 140: correlation = 0.3606722
🎯 Economical lag 141: correlation = 0.4391199
🎯 Economical lag 142: correlation = 0.51130444
🎯 Economical lag 143: correlation = 0.5100724
🎯 Economical lag 144: correlation = 0.5333897
🎯 Economical lag 145: correlation = 0.5810995
🎯 Economical lag 146: correlation = 0.61460716
🎯 Economical lag 147: correlation = 0.6574904
🎯 Economical lag 148: correlation = 0.6864612
🎯 Economical lag 149: correlation = 0.70463544
🎯 Economical lag 150: correlation = 0.7249516
🎯 Economical lag 151: correlation = 0.77969015
🎯 Economical lag 152: correlation = 0.8624322
🔍 Best correlation: 0.8624322 at period 152 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-8.77193 Hz (periods: 121-152)
📊 Moving averages updated - Period: 152.0, Amplitude: 0.8175909, RunLength: 4
✅ Pitch detected: 7.017544 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.017544 Hz
🎵 Detected pitch: 7.017544 Hz (total samples: 235200)
� Calculated pressure: 3.1936312 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.017544, Pressure: 3.1936312, Breaths: 0
🎤 Audio Level: 0.036006123, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 240000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 4.6172855e-07, Max: 0.0003480314, Avg: 5.8699014e-05, Variance: 5.4342264e-09
🎯 Economical search: one wavelength ahead 304, clamped 122, window [92, 152]
🎯 Economical lag 92: correlation = 0.42868763
🎯 Economical lag 93: correlation = 0.46169993
🎯 Economical lag 94: correlation = 0.52246326
🎯 Economical lag 95: correlation = 0.5759333
🎯 Economical lag 96: correlation = 0.60487086
🎯 Economical lag 97: correlation = 0.6627244
🎯 Economical lag 98: correlation = 0.7026877
🎯 Economical lag 99: correlation = 0.7054523
🎯 Economical lag 100: correlation = 0.68799675
🎯 Economical lag 101: correlation = 0.6902787
🎯 Economical lag 102: correlation = 0.75326824
🎯 Economical lag 103: correlation = 0.83858365
🎯 Economical lag 104: correlation = 0.8101188
🎯 Economical lag 105: correlation = 0.8070896
🎯 Economical lag 106: correlation = 0.79885477
🎯 Economical lag 107: correlation = 0.7216347
🎯 Economical lag 108: correlation = 0.69240415
🎯 Economical lag 109: correlation = 0.65464264
🎯 Economical lag 110: correlation = 0.59760815
🎯 Economical lag 111: correlation = 0.5608629
🎯 Economical lag 112: correlation = 0.5048958
🎯 Economical lag 113: correlation = 0.4930096
🎯 Economical lag 114: correlation = 0.44364125
🎯 Economical lag 115: correlation = 0.36084187
🎯 Economical lag 116: correlation = 0.321242
🎯 Economical lag 142: correlation = 0.32223654
🎯 Economical lag 143: correlation = 0.35354084
🎯 Economical lag 144: correlation = 0.39429045
🎯 Economical lag 145: correlation = 0.4193259
🎯 Economical lag 146: correlation = 0.42304313
🎯 Economical lag 147: correlation = 0.4695998
🎯 Economical lag 148: correlation = 0.46007773
🎯 Economical lag 149: correlation = 0.4425345
🎯 Economical lag 150: correlation = 0.46821094
🎯 Economical lag 151: correlation = 0.5047496
🎯 Economical lag 152: correlation = 0.59668463
🔍 Best correlation: 0.83858365 at period 103 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.7669897-12.9449835 Hz (periods: 82-137)
🔄 Run length reduced: 4 → 2 (leap: 3.3384428 Hz, threshold: 1.4035088 Hz)
📊 Moving averages updated - Period: 135.66666, Amplitude: 0.8245885, RunLength: 3
✅ Pitch detected: 10.355987 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 10.355987 Hz
🎵 Detected pitch: 10.355987 Hz (total samples: 240000)
� Calculated pressure: 6.9293485 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 10.355987, Pressure: 6.9293485, Breaths: 0
🎤 Audio Level: 0.031471267, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 244800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 2.0744984e-07, Max: 0.00043212064, Avg: 5.3360316e-05, Variance: 5.7940213e-09
🎯 Economical search: one wavelength ahead 206, clamped 132, window [112, 152]
🎯 Economical lag 112: correlation = 0.5999396
🎯 Economical lag 113: correlation = 0.57939243
🎯 Economical lag 114: correlation = 0.4925775
🎯 Economical lag 115: correlation = 0.39132953
🎯 Economical lag 116: correlation = 0.34275392
🎯 Economical lag 117: correlation = 0.3168488
🎯 Economical lag 142: correlation = 0.3117235
🎯 Economical lag 143: correlation = 0.3335358
🎯 Economical lag 144: correlation = 0.36954308
🎯 Economical lag 145: correlation = 0.4304162
🎯 Economical lag 146: correlation = 0.44101638
🎯 Economical lag 147: correlation = 0.4590444
🎯 Economical lag 148: correlation = 0.46444657
🎯 Economical lag 149: correlation = 0.45845327
🎯 Economical lag 150: correlation = 0.55031323
🎯 Economical lag 151: correlation = 0.64838827
🎯 Economical lag 152: correlation = 0.7103698
🔍 Best correlation: 0.7103698 at period 152 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-8.77193 Hz (periods: 121-152)
📊 Moving averages updated - Period: 139.75, Amplitude: 0.7960338, RunLength: 4
✅ Pitch detected: 7.017544 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.017544 Hz
🎵 Detected pitch: 7.017544 Hz (total samples: 244800)
� Calculated pressure: 3.1936312 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.017544, Pressure: 3.1936312, Breaths: 0
🎤 Audio Level: 0.029963484, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 249600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 2.0744984e-07, Max: 0.00028535, Avg: 4.063029e-05, Variance: 2.8919547e-09
🎯 Economical search: one wavelength ahead 304, clamped 122, window [92, 152]
🎯 Economical lag 92: correlation = 0.42787114
🎯 Economical lag 93: correlation = 0.41609108
🎯 Economical lag 94: correlation = 0.44125265
🎯 Economical lag 95: correlation = 0.5466621
🎯 Economical lag 96: correlation = 0.57604635
🎯 Economical lag 97: correlation = 0.54244125
🎯 Economical lag 98: correlation = 0.58883256
🎯 Economical lag 99: correlation = 0.62930924
🎯 Economical lag 100: correlation = 0.64314866
🎯 Economical lag 101: correlation = 0.6059002
🎯 Economical lag 102: correlation = 0.5308823
🎯 Economical lag 103: correlation = 0.5562752
🎯 Economical lag 104: correlation = 0.560256
🎯 Economical lag 105: correlation = 0.5741855
🎯 Economical lag 106: correlation = 0.57854706
🎯 Economical lag 107: correlation = 0.6051475
🎯 Economical lag 108: correlation = 0.60804886
🎯 Economical lag 109: correlation = 0.5571266
🎯 Economical lag 110: correlation = 0.483429
🎯 Economical lag 111: correlation = 0.4722479
🎯 Economical lag 112: correlation = 0.42744187
🎯 Economical lag 113: correlation = 0.3456822
🎯 Economical lag 114: correlation = 0.32700825
🎯 Economical lag 115: correlation = 0.30538508
🎯 Economical lag 137: correlation = 0.3348469
🎯 Economical lag 138: correlation = 0.48055872
🎯 Economical lag 139: correlation = 0.5907022
🎯 Economical lag 140: correlation = 0.5194523
🎯 Economical lag 141: correlation = 0.40951046
🎯 Economical lag 142: correlation = 0.40399766
🎯 Economical lag 143: correlation = 0.4927264
🎯 Economical lag 144: correlation = 0.59514105
🎯 Economical lag 145: correlation = 0.58198637
🎯 Economical lag 146: correlation = 0.58566296
🎯 Economical lag 147: correlation = 0.59311545
🎯 Economical lag 148: correlation = 0.64869386
🎯 Economical lag 149: correlation = 0.5960248
🎯 Economical lag 150: correlation = 0.7052374
🎯 Economical lag 151: correlation = 0.80102056
🎯 Economical lag 152: correlation = 0.7791716
🔍 Best correlation: 0.80102056 at period 151 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-8.830022 Hz (periods: 120-152)
📊 Moving averages updated - Period: 142.0, Amplitude: 0.79703116, RunLength: 5
✅ Pitch detected: 7.0640173 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.0640173 Hz
🎵 Detected pitch: 7.0640173 Hz (total samples: 249600)
� Calculated pressure: 3.245635 cm H2O
🖥️ UI updated - Freq: 7.0640173, Pressure: 3.245635, Breaths: 0
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.035671774, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 254400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 5.8850367e-07, Max: 0.0003338886, Avg: 4.764088e-05, Variance: 3.8401677e-09
🎯 Economical search: one wavelength ahead 302, clamped 122, window [92, 152]
🎯 Economical lag 92: correlation = 0.5265303
🎯 Economical lag 93: correlation = 0.50338733
🎯 Economical lag 94: correlation = 0.53148186
🎯 Economical lag 95: correlation = 0.54233783
🎯 Economical lag 96: correlation = 0.5595402
🎯 Economical lag 97: correlation = 0.61664003
🎯 Economical lag 98: correlation = 0.6598215
🎯 Economical lag 99: correlation = 0.64828676
🎯 Economical lag 100: correlation = 0.62220985
🎯 Economical lag 101: correlation = 0.645395
🎯 Economical lag 102: correlation = 0.7291777
🎯 Economical lag 103: correlation = 0.8016458
🎯 Economical lag 104: correlation = 0.7602557
🎯 Economical lag 105: correlation = 0.68977714
🎯 Economical lag 106: correlation = 0.59518445
🎯 Economical lag 107: correlation = 0.5407508
🎯 Economical lag 108: correlation = 0.5564831
🎯 Economical lag 109: correlation = 0.6588541
🎯 Economical lag 110: correlation = 0.70898634
🎯 Economical lag 111: correlation = 0.5672444
🎯 Economical lag 112: correlation = 0.43031874
🎯 Economical lag 113: correlation = 0.36013925
🎯 Economical lag 114: correlation = 0.37485623
🎯 Economical lag 115: correlation = 0.36013076
🎯 Economical lag 116: correlation = 0.32403445
🎯 Economical lag 140: correlation = 0.315656
🎯 Economical lag 141: correlation = 0.3492364
🎯 Economical lag 142: correlation = 0.38312954
🎯 Economical lag 143: correlation = 0.35459548
🎯 Economical lag 144: correlation = 0.3714802
🎯 Economical lag 145: correlation = 0.4060785
🎯 Economical lag 146: correlation = 0.43958542
🎯 Economical lag 147: correlation = 0.47361365
🎯 Economical lag 148: correlation = 0.5138735
🎯 Economical lag 149: correlation = 0.5767048
🎯 Economical lag 150: correlation = 0.60430866
🎯 Economical lag 151: correlation = 0.6014021
🎯 Economical lag 152: correlation = 0.62629783
🔍 Best correlation: 0.8016458 at period 103 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.7669897-12.9449835 Hz (periods: 82-137)
🔄 Run length reduced: 5 → 4 (leap: 2.8442497 Hz, threshold: 1.5023474 Hz)
📊 Moving averages updated - Period: 134.2, Amplitude: 0.7979541, RunLength: 5
✅ Pitch detected: 10.355987 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 10.355987 Hz
🎵 Detected pitch: 10.355987 Hz (total samples: 254400)
� Calculated pressure: 6.9293485 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 10.355987, Pressure: 6.9293485, Breaths: 0
🎤 Audio Level: 0.04348208, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 259200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 5.010932e-07, Max: 0.0005148473, Avg: 6.292433e-05, Variance: 7.928745e-09
🎯 Economical search: one wavelength ahead 206, clamped 132, window [112, 152]
🎯 Economical lag 112: correlation = 0.5424487
🎯 Economical lag 113: correlation = 0.5246412
🎯 Economical lag 114: correlation = 0.5324591
🎯 Economical lag 115: correlation = 0.44010764
🎯 Economical lag 116: correlation = 0.3176723
🎯 Economical lag 146: correlation = 0.3153572
🎯 Economical lag 147: correlation = 0.35567468
🎯 Economical lag 148: correlation = 0.43907735
🎯 Economical lag 149: correlation = 0.4900835
🎯 Economical lag 150: correlation = 0.46774846
🎯 Economical lag 151: correlation = 0.4256073
🎯 Economical lag 152: correlation = 0.4397414
🔄 Economical search failed (best: 0.5424487), falling back to full search
🔍 Performing autocorrelation search
🔍 Lag 26: corr=1.1274289e-07, norm1=1.6774216e-06, norm2=2.3565692e-06, result=0.056705862
🔍 Coarse search lag 26: correlation = 0.056705862
🔍 Lag 29: corr=1.5007242e-07, norm1=1.6774012e-06, norm2=2.3509958e-06, result=0.075571224
🔍 Coarse search lag 29: correlation = 0.075571224
🔍 Coarse search lag 32: correlation = 0.0914343
🔍 Coarse search lag 41: correlation = 0.36715046
🔍 Coarse search lag 44: correlation = 0.44980392
🔍 Coarse search lag 47: correlation = 0.6315555
🔍 Coarse search lag 50: correlation = 0.6801431
🔍 Coarse search lag 53: correlation = 0.72032416
🔍 Coarse search lag 56: correlation = 0.6269837
🔍 Coarse search lag 59: correlation = 0.63624644
🔍 Coarse search lag 62: correlation = 0.5169636
🔍 Coarse search lag 65: correlation = 0.31031445
🔍 Coarse search lag 95: correlation = 0.42486867
🔍 Coarse search lag 98: correlation = 0.6503568
🔍 Coarse search lag 101: correlation = 0.8025411
🔍 Coarse search lag 104: correlation = 0.74955344
🔍 Coarse search lag 107: correlation = 0.7586318
🔍 Coarse search lag 110: correlation = 0.7008369
🔍 Coarse search lag 113: correlation = 0.5246412
🔍 Coarse search lag 116: correlation = 0.3176723
🔍 Coarse search lag 146: correlation = 0.3153572
🔍 Coarse search lag 149: correlation = 0.4900835
🔍 Coarse search lag 152: correlation = 0.4397414
🔍 Fine search around lag 101 in range [99, 103]
🔍 Fine search lag 99: correlation = 0.6910968
🔍 Fine search lag 100: correlation = 0.7474513
🔍 Fine search lag 101: correlation = 0.8025411
🔍 Fine search lag 102: correlation = 0.825009
🔍 Fine search lag 103: correlation = 0.76765275
🔍 Best correlation: 0.825009 at period 102 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.843137-13.071895 Hz (periods: 81-136)
🔄 Run length reduced: 5 → 4 (leap: 2.50918 Hz, threshold: 1.5896672 Hz)
📊 Moving averages updated - Period: 127.76, Amplitude: 0.8033651, RunLength: 5
✅ Pitch detected: 10.457516 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 10.457516 Hz
🎵 Detected pitch: 10.457516 Hz (total samples: 259200)
� Calculated pressure: 7.0429597 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 10.457516, Pressure: 7.0429597, Breaths: 0
🎤 Audio Level: 0.02909673, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 264000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.2098036e-07, Max: 0.0005148473, Avg: 5.2956046e-05, Variance: 6.218831e-09
🎯 Economical search: one wavelength ahead 204, clamped 132, window [112, 152]
🎯 Economical lag 112: correlation = 0.7645119
🎯 Economical lag 113: correlation = 0.76632994
🎯 Economical lag 114: correlation = 0.69961494
🎯 Economical lag 115: correlation = 0.6405158
🎯 Economical lag 116: correlation = 0.5967767
🎯 Economical lag 117: correlation = 0.54728675
🎯 Economical lag 118: correlation = 0.5211837
🎯 Economical lag 119: correlation = 0.50387114
🎯 Economical lag 120: correlation = 0.48445183
🎯 Economical lag 121: correlation = 0.44422448
🎯 Economical lag 122: correlation = 0.3680728
🎯 Economical lag 123: correlation = 0.30743393
🎯 Economical lag 149: correlation = 0.3390392
🎯 Economical lag 150: correlation = 0.42379323
🎯 Economical lag 151: correlation = 0.5012209
🎯 Economical lag 152: correlation = 0.50921
🔍 Best correlation: 0.76632994 at period 113 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0796456-11.79941 Hz (periods: 90-150)
📊 Moving averages updated - Period: 124.808, Amplitude: 0.79595804, RunLength: 5
✅ Pitch detected: 9.4395275 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.4395275 Hz
🎵 Detected pitch: 9.4395275 Hz (total samples: 264000)
� Calculated pressure: 5.903831 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.4395275, Pressure: 5.903831, Breaths: 0
🎤 Audio Level: 0.025357705, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 268800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.2098036e-07, Max: 0.0002591031, Avg: 3.9127906e-05, Variance: 2.700686e-09
🎯 Economical search: one wavelength ahead 226, clamped 130, window [108, 152]
🎯 Economical lag 108: correlation = 0.7865962
🎯 Economical lag 109: correlation = 0.8215073
🎯 Economical lag 110: correlation = 0.83097005
🎯 Economical lag 111: correlation = 0.7645459
🎯 Economical lag 112: correlation = 0.72814924
🎯 Economical lag 113: correlation = 0.7032556
🎯 Economical lag 114: correlation = 0.60718477
🎯 Economical lag 115: correlation = 0.5978221
🎯 Economical lag 116: correlation = 0.53117687
🎯 Economical lag 117: correlation = 0.45621097
🎯 Economical lag 118: correlation = 0.48212922
🎯 Economical lag 119: correlation = 0.43860382
🎯 Economical lag 120: correlation = 0.37293497
🎯 Economical lag 121: correlation = 0.34069452
🎯 Economical lag 148: correlation = 0.30903298
🎯 Economical lag 149: correlation = 0.3487893
🎯 Economical lag 150: correlation = 0.4351443
🎯 Economical lag 151: correlation = 0.50036067
🎯 Economical lag 152: correlation = 0.51414037
🔍 Best correlation: 0.83097005 at period 110 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.272727-12.121211 Hz (periods: 88-146)
📊 Moving averages updated - Period: 121.8464, Amplitude: 0.80296046, RunLength: 5
✅ Pitch detected: 9.696969 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.696969 Hz
🎵 Detected pitch: 9.696969 Hz (total samples: 268800)
� Calculated pressure: 6.1919084 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.696969, Pressure: 6.1919084, Breaths: 0
🎤 Audio Level: 0.024550412, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 273600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 2.2263812e-07, Max: 0.00019926533, Avg: 2.8782102e-05, Variance: 1.4581625e-09
🎯 Economical search: one wavelength ahead 220, clamped 130, window [108, 152]
🎯 Economical lag 108: correlation = 0.34690556
🎯 Economical lag 109: correlation = 0.36394957
🎯 Economical lag 110: correlation = 0.40152055
🎯 Economical lag 111: correlation = 0.43975472
🎯 Economical lag 112: correlation = 0.5149022
🎯 Economical lag 113: correlation = 0.5334146
🎯 Economical lag 114: correlation = 0.50346005
🎯 Economical lag 115: correlation = 0.51824313
🎯 Economical lag 116: correlation = 0.5144993
🎯 Economical lag 117: correlation = 0.5609027
🎯 Economical lag 118: correlation = 0.6830623
🎯 Economical lag 119: correlation = 0.7150523
🎯 Economical lag 120: correlation = 0.768912
🎯 Economical lag 121: correlation = 0.7943661
🎯 Economical lag 122: correlation = 0.7911514
🎯 Economical lag 123: correlation = 0.8028637
🎯 Economical lag 124: correlation = 0.7401783
🎯 Economical lag 125: correlation = 0.76191884
🎯 Economical lag 126: correlation = 0.7484566
🎯 Economical lag 127: correlation = 0.72608143
🎯 Economical lag 128: correlation = 0.7458002
🎯 Economical lag 129: correlation = 0.6862824
🎯 Economical lag 130: correlation = 0.6054274
🎯 Economical lag 131: correlation = 0.55464584
🎯 Economical lag 132: correlation = 0.52331144
🎯 Economical lag 133: correlation = 0.5191297
🎯 Economical lag 134: correlation = 0.4343533
🎯 Economical lag 135: correlation = 0.3725417
🎯 Economical lag 136: correlation = 0.34531242
🎯 Economical lag 137: correlation = 0.3164875
🎯 Economical lag 138: correlation = 0.30021355
🔍 Best correlation: 0.8028637 at period 123 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-10.840109 Hz (periods: 98-152)
📊 Moving averages updated - Period: 122.07712, Amplitude: 0.8029411, RunLength: 5
✅ Pitch detected: 8.672087 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 8.672087 Hz
🎵 Detected pitch: 8.672087 Hz (total samples: 273600)
� Calculated pressure: 5.0450644 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 8.672087, Pressure: 5.0450644, Breaths: 0
🎤 Audio Level: 0.0033046121, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 278400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 2.0080777e-09, Max: 0.00016311296, Avg: 1.2287671e-05, Variance: 8.7817953e-10
🎯 Economical search: one wavelength ahead 246, clamped 128, window [104, 152]
🎯 Economical lag 104: correlation = 0.30816138
🎯 Economical lag 105: correlation = 0.30593443
🎯 Economical lag 139: correlation = 0.33407143
🎯 Economical lag 140: correlation = 0.3667806
🎯 Economical lag 141: correlation = 0.3275683
🎯 Economical lag 144: correlation = 0.30556923
🎯 Economical lag 145: correlation = 0.3861488
🎯 Economical lag 146: correlation = 0.40607056
🎯 Economical lag 147: correlation = 0.486899
🎯 Economical lag 148: correlation = 0.65828484
🎯 Economical lag 149: correlation = 0.85430896
🎯 Economical lag 150: correlation = 0.8665759
🎯 Economical lag 151: correlation = 0.7698464
🎯 Economical lag 152: correlation = 0.7153988
🔍 Best correlation: 0.8665759 at period 150 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-8.888888 Hz (periods: 120-152)
📊 Moving averages updated - Period: 127.6617, Amplitude: 0.81566805, RunLength: 5
✅ Pitch detected: 7.1111107 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.1111107 Hz
🎵 Detected pitch: 7.1111107 Hz (total samples: 278400)
� Calculated pressure: 3.2983327 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.1111107, Pressure: 3.2983327, Breaths: 0
🎤 Audio Level: 0.023070384, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 283200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.97122e-09, Max: 0.00013295423, Avg: 2.7771757e-06, Variance: 1.8750258e-10
🎯 Economical search: one wavelength ahead 300, clamped 122, window [92, 152]
🔄 Economical search failed (best: 0.10063218), falling back to full search
🔍 Performing autocorrelation search
🔍 Lag 26: corr=4.694401e-10, norm1=3.904298e-08, norm2=3.9041943e-08, result=0.0120238345
🔍 Coarse search lag 26: correlation = 0.0120238345
🔍 Lag 29: corr=1.7167918e-10, norm1=3.904298e-08, norm2=3.904166e-08, result=0.0043972586
🔍 Coarse search lag 29: correlation = 0.0043972586
🔍 Coarse search lag 32: correlation = 0.003314438
🔍 Fine search around lag 104 in range [102, 106]
🔍 Best correlation: 0.10063218 at period 104 (threshold: 0.6) [strategy: fallback]
❌ No pitch detected - best correlation 0.10063218 below threshold 0.6
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 283200)
� Calculated pressure: 0.0 cm H2O
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.00071775605, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 288000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.4154684e-09, Max: 0.00013295423, Avg: 2.570554e-06, Variance: 1.8830779e-10
🎯 Economical search: one wavelength ahead 300, clamped 122, window [92, 152]
🔄 Economical search failed (best: 0.0), falling back to full search
🔍 Performing autocorrelation search
🔍 Lag 26: corr=4.407852e-10, norm1=3.8983075e-08, norm2=6.254112e-12, result=0.8927012
🔍 Coarse search lag 26: correlation = 0.8927012
🔍 Lag 29: corr=1.4583079e-10, norm1=3.8983075e-08, norm2=1.4110715e-12, result=0.6217799
🔍 Coarse search lag 29: correlation = 0.6217799
🔍 Coarse search lag 32: correlation = 0.7362043
🔍 Fine search around lag 26 in range [26, 28]
🔍 Lag 26: corr=4.407852e-10, norm1=3.8983075e-08, norm2=6.254112e-12, result=0.8927012
🔍 Fine search lag 26: correlation = 0.8927012
🔍 Lag 27: corr=3.3538136e-10, norm1=3.8983075e-08, norm2=5.1490795e-12, result=0.7485763
🔍 Fine search lag 27: correlation = 0.7485763
🔍 Lag 28: corr=2.0947054e-10, norm1=3.8983075e-08, norm2=3.3119692e-12, result=0.58296406
🔍 Fine search lag 28: correlation = 0.58296406
🔍 Best correlation: 0.8927012 at period 26 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 30.76923-40.0 Hz (periods: 26-34)
🔄 Run length reduced: 5 → 0 (leap: 32.670223 Hz, threshold: 1.6710833 Hz)
🔄 Moving averages reset due to run length = 0
✅ Pitch detected: 41.02564 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 41.02564 Hz
🎵 Detected pitch: 41.02564 Hz (total samples: 288000)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 41.02564, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0004239985, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 292800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 8.5840524e-10, Max: 6.865294e-08, Avg: 7.914991e-09, Variance: 5.091101e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 292800)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0035773823, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 297600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 8.5840524e-10, Max: 4.5310117e-06, Avg: 6.996622e-08, Variance: 1.3586112e-13
🎯 Economical search: one wavelength ahead 52, clamped 52, window [47, 57]
🔄 Economical search failed (best: 0.0), falling back to full search
🔍 Performing autocorrelation search
🔍 Lag 26: corr=2.9601826e-13, norm1=2.761387e-11, norm2=2.8148124e-11, result=0.0
🔍 Coarse search lag 26: correlation = 0.0
🔍 Lag 29: corr=2.824441e-13, norm1=2.7609358e-11, norm2=2.8147831e-11, result=0.0
🔍 Coarse search lag 29: correlation = 0.0
🔍 Coarse search lag 32: correlation = 0.0
🔍 Best correlation: 0.0 at period 0 (threshold: 0.6) [strategy: fallback]
❌ No pitch detected - best correlation 0.0 below threshold 0.6
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 297600)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🫁 Breath detection: Entering ending state (silence: 0.3s)
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0011890396, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 302400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.0167905e-09, Max: 4.5310117e-06, Avg: 9.88802e-08, Variance: 1.3404073e-13
🎯 Economical search: one wavelength ahead 52, clamped 52, window [47, 57]
🔄 Economical search failed (best: 0.0), falling back to full search
🔍 Performing autocorrelation search
🔍 Lag 26: corr=6.6904473e-13, norm1=2.8568791e-11, norm2=2.8531153e-12, result=0.0
🔍 Coarse search lag 26: correlation = 0.0
🔍 Lag 29: corr=5.7108317e-13, norm1=2.8558003e-11, norm2=1.7136249e-12, result=0.0
🔍 Coarse search lag 29: correlation = 0.0
🔍 Coarse search lag 32: correlation = 0.0
🔍 Best correlation: 0.0 at period 0 (threshold: 0.6) [strategy: fallback]
❌ No pitch detected - best correlation 0.0 below threshold 0.6
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 302400)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🫁 Breath detection: Completed breath #1 (duration: 2.8s)
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.0012737724, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 307200
🫁 Recorded breath duration: 2.8s (Total: 1)
🫁 Breath completed for step 1: duration=2.8s, performance=completedAmber
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.3657912e-08, Max: 3.4925768e-07, Avg: 7.938589e-08, Variance: 3.2217178e-15
🎯 Economical search: one wavelength ahead 52, clamped 52, window [47, 57]
🔄 Economical search failed (best: 0.0), falling back to full search
🔍 Performing autocorrelation search
🔍 Lag 26: corr=1.1413197e-12, norm1=1.5681686e-12, norm2=1.8306567e-12, result=0.0
🔍 Coarse search lag 26: correlation = 0.0
🔍 Lag 29: corr=1.0545834e-12, norm1=1.5435444e-12, norm2=1.7949765e-12, result=0.0
🔍 Coarse search lag 29: correlation = 0.0
🔍 Coarse search lag 32: correlation = 0.0
🔍 Best correlation: 0.0 at period 0 (threshold: 0.6) [strategy: fallback]
❌ No pitch detected - best correlation 0.0 below threshold 0.6
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 307200)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.0010522476, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 312000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 8.57973e-09, Max: 3.8074023e-07, Avg: 8.3181156e-08, Variance: 3.5102991e-15
🎯 Economical search: one wavelength ahead 52, clamped 52, window [47, 57]
🔄 Economical search failed (best: 0.0), falling back to full search
🔍 Performing autocorrelation search
🔍 Lag 26: corr=1.2794677e-12, norm1=1.9801136e-12, norm2=1.9340922e-12, result=0.0
🔍 Coarse search lag 26: correlation = 0.0
🔍 Lag 29: corr=1.2378422e-12, norm1=1.9766177e-12, norm2=1.9032406e-12, result=0.0
🔍 Coarse search lag 29: correlation = 0.0
🔍 Coarse search lag 32: correlation = 0.0
🔍 Best correlation: 0.0 at period 0 (threshold: 0.6) [strategy: fallback]
❌ No pitch detected - best correlation 0.0 below threshold 0.6
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 312000)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.0008671612, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 316800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 5.6364917e-09, Max: 3.8074023e-07, Avg: 6.786054e-08, Variance: 2.4616188e-15
🎯 Economical search: one wavelength ahead 52, clamped 52, window [47, 57]
🔄 Economical search failed (best: 0.0), falling back to full search
🔍 Performing autocorrelation search
🔍 Lag 26: corr=8.214187e-13, norm1=1.3015198e-12, norm2=1.2105084e-12, result=0.0
🔍 Coarse search lag 26: correlation = 0.0
🔍 Lag 29: corr=7.5749487e-13, norm1=1.2875645e-12, norm2=1.1792808e-12, result=0.0
🔍 Coarse search lag 29: correlation = 0.0
🔍 Coarse search lag 32: correlation = 0.0
🔍 Best correlation: 0.0 at period 0 (threshold: 0.6) [strategy: fallback]
❌ No pitch detected - best correlation 0.0 below threshold 0.6
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 316800)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.0009611174, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 321600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 5.0100835e-09, Max: 2.5473398e-07, Avg: 6.070123e-08, Variance: 2.1012833e-15
🎯 Economical search: one wavelength ahead 52, clamped 52, window [47, 57]
🔄 Economical search failed (best: 0.0), falling back to full search
🔍 Performing autocorrelation search
🔍 Lag 26: corr=6.1865465e-13, norm1=1.0603597e-12, norm2=9.118754e-13, result=0.0
🔍 Coarse search lag 26: correlation = 0.0
🔍 Lag 29: corr=6.0358294e-13, norm1=1.0595945e-12, norm2=8.9456285e-13, result=0.0
🔍 Coarse search lag 29: correlation = 0.0
🔍 Coarse search lag 32: correlation = 0.0
🔍 Best correlation: 0.0 at period 0 (threshold: 0.6) [strategy: fallback]
❌ No pitch detected - best correlation 0.0 below threshold 0.6
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 321600)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.00065463997, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 326400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.1558998e-09, Max: 2.5473398e-07, Avg: 4.5569855e-08, Variance: 1.6537239e-15
🎯 Economical search: one wavelength ahead 52, clamped 52, window [47, 57]
🔄 Economical search failed (best: 0.0), falling back to full search
🔍 Performing autocorrelation search
🔍 Lag 26: corr=3.7186725e-13, norm1=6.9520106e-13, norm2=5.729982e-13, result=0.0
🔍 Coarse search lag 26: correlation = 0.0
🔍 Lag 29: corr=3.926948e-13, norm1=6.8799144e-13, norm2=5.5783205e-13, result=0.0
🔍 Coarse search lag 29: correlation = 0.0
🔍 Coarse search lag 32: correlation = 0.0
🔍 Best correlation: 0.0 at period 0 (threshold: 0.6) [strategy: fallback]
❌ No pitch detected - best correlation 0.0 below threshold 0.6
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 326400)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.00052429776, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 331200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.1558998e-09, Max: 1.4074848e-07, Avg: 2.5933423e-08, Variance: 4.2238473e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 331200)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.0006448107, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 336000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.5047594e-09, Max: 5.1407934e-08, Avg: 1.3497322e-08, Variance: 1.0737508e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 336000)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.043162555, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 340800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.5047594e-09, Max: 0.0005318008, Avg: 3.5952813e-05, Variance: 6.4563532e-09
🎯 Economical search: one wavelength ahead 52, clamped 52, window [47, 57]
🎯 Economical lag 47: correlation = 0.48637378
🎯 Economical lag 48: correlation = 0.39539775
🎯 Economical lag 49: correlation = 0.35571852
🎯 Economical lag 50: correlation = 0.37652463
🎯 Economical lag 51: correlation = 0.41176996
🎯 Economical lag 52: correlation = 0.36217707
🎯 Economical lag 53: correlation = 0.31562608
🔄 Economical search failed (best: 0.48637378), falling back to full search
🔍 Performing autocorrelation search
🔍 Lag 26: corr=6.427412e-08, norm1=9.737116e-07, norm2=1.5497917e-06, result=0.052321993
🔍 Coarse search lag 26: correlation = 0.052321993
🔍 Lag 29: corr=1.0152323e-07, norm1=9.734852e-07, norm2=1.5497917e-06, result=0.082654044
🔍 Coarse search lag 29: correlation = 0.082654044
🔍 Coarse search lag 32: correlation = 0.16475536
🔍 Coarse search lag 38: correlation = 0.40313762
🔍 Coarse search lag 41: correlation = 0.43471992
🔍 Coarse search lag 44: correlation = 0.48797825
🔍 Coarse search lag 47: correlation = 0.48637378
🔍 Coarse search lag 50: correlation = 0.37652463
🔍 Coarse search lag 53: correlation = 0.31562608
🔍 Coarse search lag 59: correlation = 0.31895235
🔍 Coarse search lag 92: correlation = 0.40345109
🔍 Coarse search lag 95: correlation = 0.57474774
🔍 Coarse search lag 98: correlation = 0.53783387
🔍 Coarse search lag 101: correlation = 0.5788016
🔍 Coarse search lag 104: correlation = 0.5341442
🔍 Coarse search lag 107: correlation = 0.48659766
🔍 Coarse search lag 110: correlation = 0.58921444
🔍 Coarse search lag 113: correlation = 0.5408347
🔍 Coarse search lag 116: correlation = 0.52494055
🔍 Coarse search lag 119: correlation = 0.52775013
🔍 Coarse search lag 122: correlation = 0.4570436
🔍 Coarse search lag 125: correlation = 0.4848498
🔍 Coarse search lag 128: correlation = 0.4826831
🔍 Coarse search lag 131: correlation = 0.48686975
🔍 Fine search around lag 110 in range [108, 112]
🔍 Fine search lag 108: correlation = 0.53999
🔍 Fine search lag 109: correlation = 0.58284
🔍 Fine search lag 110: correlation = 0.58921444
🔍 Fine search lag 111: correlation = 0.5705517
🔍 Fine search lag 112: correlation = 0.56712335
🔍 Best correlation: 0.58921444 at period 110 (threshold: 0.6) [strategy: fallback]
❌ No pitch detected - best correlation 0.58921444 below threshold 0.6
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 340800)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🫁 Breath detection: Starting new breath
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.058953054, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 345600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 4.092997e-09, Max: 0.0008674572, Avg: 0.00010021069, Variance: 1.836404e-08
🎯 Economical search: one wavelength ahead 52, clamped 52, window [47, 57]
🎯 Economical lag 47: correlation = 0.76274306
🎯 Economical lag 48: correlation = 0.70999444
🎯 Economical lag 49: correlation = 0.637358
🎯 Economical lag 50: correlation = 0.58441657
🎯 Economical lag 51: correlation = 0.5633732
🎯 Economical lag 52: correlation = 0.55121785
🎯 Economical lag 53: correlation = 0.533903
🎯 Economical lag 54: correlation = 0.48212802
🎯 Economical lag 55: correlation = 0.40408278
🎯 Economical lag 56: correlation = 0.341433
🎯 Economical lag 57: correlation = 0.30710298
🔍 Best correlation: 0.76274306 at period 47 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 17.021275-28.368793 Hz (periods: 37-62)
📊 Moving averages updated - Period: 47.0, Amplitude: 0.76274306, RunLength: 1
✅ Pitch detected: 22.695034 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 22.695034 Hz
🎵 Detected pitch: 22.695034 Hz (total samples: 345600)
� Calculated pressure: 20.736742 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 22.695034, Pressure: 20.736742, Breaths: 1
🎤 Audio Level: 0.06402623, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 350400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 2.335848e-06, Max: 0.0010858189, Avg: 0.00017036326, Variance: 3.964864e-08
🎯 Economical search: one wavelength ahead 94, clamped 94, window [85, 103]
🎯 Economical lag 85: correlation = 0.8011623
🎯 Economical lag 86: correlation = 0.7895201
🎯 Economical lag 87: correlation = 0.75508094
🎯 Economical lag 88: correlation = 0.69977236
🎯 Economical lag 89: correlation = 0.6475389
🎯 Economical lag 90: correlation = 0.63955146
🎯 Economical lag 91: correlation = 0.6225342
🎯 Economical lag 92: correlation = 0.54947037
🎯 Economical lag 93: correlation = 0.48176646
🎯 Economical lag 94: correlation = 0.43687814
🎯 Economical lag 95: correlation = 0.39438745
🎯 Economical lag 96: correlation = 0.34530914
🎯 Economical lag 97: correlation = 0.30552506
🔍 Best correlation: 0.8011623 at period 85 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 9.411764-15.686274 Hz (periods: 68-113)
🔄 Run length reduced: 1 → 0 (leap: 10.146015 Hz, threshold: 4.5390067 Hz)
🔄 Moving averages reset due to run length = 0
✅ Pitch detected: 12.549019 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 12.549019 Hz
🎵 Detected pitch: 12.549019 Hz (total samples: 350400)
� Calculated pressure: 9.383352 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 12.549019, Pressure: 9.383352, Breaths: 1
🎤 Audio Level: 0.054484393, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 355200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 5.0397716e-06, Max: 0.0010858189, Avg: 0.00017630667, Variance: 3.673829e-08
🎯 Economical search: one wavelength ahead 170, clamped 135, window [118, 152]
🎯 Economical lag 118: correlation = 0.63448066
🎯 Economical lag 119: correlation = 0.68619215
🎯 Economical lag 120: correlation = 0.76063615
🎯 Economical lag 121: correlation = 0.7850782
🎯 Economical lag 122: correlation = 0.7499552
🎯 Economical lag 123: correlation = 0.7239212
🎯 Economical lag 124: correlation = 0.6949362
🎯 Economical lag 125: correlation = 0.68259454
🎯 Economical lag 126: correlation = 0.6140758
🎯 Economical lag 127: correlation = 0.60782784
🎯 Economical lag 128: correlation = 0.6141224
🎯 Economical lag 129: correlation = 0.64696723
🎯 Economical lag 130: correlation = 0.6502686
🎯 Economical lag 131: correlation = 0.56298524
🎯 Economical lag 132: correlation = 0.43856627
🎯 Economical lag 133: correlation = 0.400975
🎯 Economical lag 134: correlation = 0.39640382
🎯 Economical lag 135: correlation = 0.39723194
🎯 Economical lag 136: correlation = 0.4246538
🎯 Economical lag 137: correlation = 0.4241572
🎯 Economical lag 138: correlation = 0.34647223
🎯 Economical lag 139: correlation = 0.3308132
🎯 Economical lag 142: correlation = 0.30189252
🎯 Economical lag 143: correlation = 0.309168
🎯 Economical lag 144: correlation = 0.30196434
🎯 Economical lag 145: correlation = 0.3111621
🎯 Economical lag 146: correlation = 0.31761864
🎯 Economical lag 148: correlation = 0.31329232
🎯 Economical lag 149: correlation = 0.3246625
🎯 Economical lag 150: correlation = 0.3451552
🎯 Economical lag 151: correlation = 0.36483198
🎯 Economical lag 152: correlation = 0.41301408
🔍 Best correlation: 0.7850782 at period 121 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-11.019283 Hz (periods: 96-152)
📊 Moving averages updated - Period: 121.0, Amplitude: 0.7850782, RunLength: 1
✅ Pitch detected: 8.815427 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 8.815427 Hz
🎵 Detected pitch: 8.815427 Hz (total samples: 355200)
� Calculated pressure: 5.205462 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 8.815427, Pressure: 5.205462, Breaths: 1
🎤 Audio Level: 0.05702632, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 360000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.20587865e-05, Max: 0.00096577854, Avg: 0.00020006397, Variance: 3.7265362e-08
🎯 Economical search: one wavelength ahead 242, clamped 128, window [104, 152]
🎯 Economical lag 104: correlation = 0.42221946
🎯 Economical lag 105: correlation = 0.42670175
🎯 Economical lag 106: correlation = 0.49551037
🎯 Economical lag 107: correlation = 0.52467215
🎯 Economical lag 108: correlation = 0.5627835
🎯 Economical lag 109: correlation = 0.61011046
🎯 Economical lag 110: correlation = 0.6319931
🎯 Economical lag 111: correlation = 0.66629547
🎯 Economical lag 112: correlation = 0.74504584
🎯 Economical lag 113: correlation = 0.7595648
🎯 Economical lag 114: correlation = 0.820416
🎯 Economical lag 115: correlation = 0.7905401
🎯 Economical lag 116: correlation = 0.7651819
🎯 Economical lag 117: correlation = 0.75028974
🎯 Economical lag 118: correlation = 0.79897547
🎯 Economical lag 119: correlation = 0.7701896
🎯 Economical lag 120: correlation = 0.79599494
🎯 Economical lag 121: correlation = 0.7315244
🎯 Economical lag 122: correlation = 0.692566
🎯 Economical lag 123: correlation = 0.62295973
🎯 Economical lag 124: correlation = 0.5695512
🎯 Economical lag 125: correlation = 0.49495438
🎯 Economical lag 126: correlation = 0.4616976
🎯 Economical lag 127: correlation = 0.4169633
🎯 Economical lag 128: correlation = 0.36093518
🎯 Economical lag 129: correlation = 0.31949568
🎯 Economical lag 138: correlation = 0.30896786
🎯 Economical lag 139: correlation = 0.31779
🎯 Economical lag 140: correlation = 0.32861963
🎯 Economical lag 141: correlation = 0.35329625
🎯 Economical lag 142: correlation = 0.3522558
🎯 Economical lag 143: correlation = 0.43668854
🎯 Economical lag 144: correlation = 0.5286286
🎯 Economical lag 145: correlation = 0.5297509
🎯 Economical lag 146: correlation = 0.5752877
🎯 Economical lag 147: correlation = 0.555761
🎯 Economical lag 148: correlation = 0.55302685
🎯 Economical lag 149: correlation = 0.64647883
🎯 Economical lag 150: correlation = 0.7260873
🎯 Economical lag 151: correlation = 0.7105769
🎯 Economical lag 152: correlation = 0.8205627
🔍 Best correlation: 0.8205627 at period 152 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-8.77193 Hz (periods: 121-152)
🔄 Run length reduced: 1 → 0 (leap: 1.797883 Hz, threshold: 1.7630854 Hz)
🔄 Moving averages reset due to run length = 0
✅ Pitch detected: 7.017544 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.017544 Hz
🎵 Detected pitch: 7.017544 Hz (total samples: 360000)
� Calculated pressure: 3.1936312 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.017544, Pressure: 3.1936312, Breaths: 1
🎤 Audio Level: 0.07237615, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 364800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 5.7939064e-06, Max: 0.0011783123, Avg: 0.00021244647, Variance: 4.7295927e-08
🎯 Economical search: one wavelength ahead 304, clamped 122, window [92, 152]
🎯 Economical lag 96: correlation = 0.31275097
🎯 Economical lag 97: correlation = 0.33466256
🎯 Economical lag 98: correlation = 0.3529618
🎯 Economical lag 99: correlation = 0.41575012
🎯 Economical lag 100: correlation = 0.45549798
🎯 Economical lag 101: correlation = 0.5010643
🎯 Economical lag 102: correlation = 0.5412964
🎯 Economical lag 103: correlation = 0.5762714
🎯 Economical lag 104: correlation = 0.62349814
🎯 Economical lag 105: correlation = 0.6484094
🎯 Economical lag 106: correlation = 0.7170155
🎯 Economical lag 107: correlation = 0.78115326
🎯 Economical lag 108: correlation = 0.836763
🎯 Economical lag 109: correlation = 0.8496019
🎯 Economical lag 110: correlation = 0.84725744
🎯 Economical lag 111: correlation = 0.8047318
🎯 Economical lag 112: correlation = 0.8169934
🎯 Economical lag 113: correlation = 0.7664883
🎯 Economical lag 114: correlation = 0.7184484
🎯 Economical lag 115: correlation = 0.6834233
🎯 Economical lag 116: correlation = 0.61369413
🎯 Economical lag 117: correlation = 0.56487745
🎯 Economical lag 118: correlation = 0.5237544
🎯 Economical lag 119: correlation = 0.50714064
🎯 Economical lag 120: correlation = 0.45423183
🎯 Economical lag 121: correlation = 0.405673
🎯 Economical lag 122: correlation = 0.3755194
🎯 Economical lag 123: correlation = 0.33148074
🎯 Economical lag 124: correlation = 0.3105912
🎯 Economical lag 131: correlation = 0.31039092
🎯 Economical lag 132: correlation = 0.31273964
🎯 Economical lag 133: correlation = 0.31457543
🎯 Economical lag 134: correlation = 0.32931945
🎯 Economical lag 135: correlation = 0.3657962
🎯 Economical lag 136: correlation = 0.39574414
🎯 Economical lag 137: correlation = 0.43951425
🎯 Economical lag 138: correlation = 0.49297997
🎯 Economical lag 139: correlation = 0.575303
🎯 Economical lag 140: correlation = 0.5968949
🎯 Economical lag 141: correlation = 0.6444597
🎯 Economical lag 142: correlation = 0.7304221
🎯 Economical lag 143: correlation = 0.7587925
🎯 Economical lag 144: correlation = 0.8263265
🎯 Economical lag 145: correlation = 0.82793087
🎯 Economical lag 146: correlation = 0.8822479
🎯 Economical lag 147: correlation = 0.87105185
🎯 Economical lag 148: correlation = 0.7908304
🎯 Economical lag 149: correlation = 0.7900615
🎯 Economical lag 150: correlation = 0.7481415
🎯 Economical lag 151: correlation = 0.6652395
🎯 Economical lag 152: correlation = 0.5812128
🔍 Best correlation: 0.8822479 at period 146 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-9.13242 Hz (periods: 116-152)
📊 Moving averages updated - Period: 146.0, Amplitude: 0.8822479, RunLength: 1
✅ Pitch detected: 7.305936 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.305936 Hz
🎵 Detected pitch: 7.305936 Hz (total samples: 364800)
� Calculated pressure: 3.5163417 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.305936, Pressure: 3.5163417, Breaths: 1
🎤 Audio Level: 0.069108516, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 369600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 5.7939064e-06, Max: 0.0011783123, Avg: 0.00021870335, Variance: 4.2482935e-08
🎯 Economical search: one wavelength ahead 292, clamped 123, window [94, 152]
🎯 Economical lag 94: correlation = 0.32052332
🎯 Economical lag 95: correlation = 0.33599415
🎯 Economical lag 96: correlation = 0.35171676
🎯 Economical lag 97: correlation = 0.37562558
🎯 Economical lag 98: correlation = 0.42573726
🎯 Economical lag 99: correlation = 0.44489482
🎯 Economical lag 100: correlation = 0.45565316
🎯 Economical lag 101: correlation = 0.49340275
🎯 Economical lag 102: correlation = 0.53530324
🎯 Economical lag 103: correlation = 0.59122837
🎯 Economical lag 104: correlation = 0.6149522
🎯 Economical lag 105: correlation = 0.6838886
🎯 Economical lag 106: correlation = 0.76091546
🎯 Economical lag 107: correlation = 0.76214343
🎯 Economical lag 108: correlation = 0.8258557
🎯 Economical lag 109: correlation = 0.8426245
🎯 Economical lag 110: correlation = 0.82736605
🎯 Economical lag 111: correlation = 0.80100864
🎯 Economical lag 112: correlation = 0.7904947
🎯 Economical lag 113: correlation = 0.79090303
🎯 Economical lag 114: correlation = 0.7543417
🎯 Economical lag 115: correlation = 0.69112164
🎯 Economical lag 116: correlation = 0.6264341
🎯 Economical lag 117: correlation = 0.5749604
🎯 Economical lag 118: correlation = 0.49607402
🎯 Economical lag 119: correlation = 0.44793922
🎯 Economical lag 120: correlation = 0.4215749
🎯 Economical lag 121: correlation = 0.38840178
🎯 Economical lag 122: correlation = 0.3542756
🎯 Economical lag 123: correlation = 0.3181165
🎯 Economical lag 124: correlation = 0.30251026
🎯 Economical lag 130: correlation = 0.32053888
🎯 Economical lag 131: correlation = 0.33997437
🎯 Economical lag 132: correlation = 0.37212285
🎯 Economical lag 133: correlation = 0.42928594
🎯 Economical lag 134: correlation = 0.45907277
🎯 Economical lag 135: correlation = 0.50519997
🎯 Economical lag 136: correlation = 0.5787879
🎯 Economical lag 137: correlation = 0.6408108
🎯 Economical lag 138: correlation = 0.67401844
🎯 Economical lag 139: correlation = 0.71575963
🎯 Economical lag 140: correlation = 0.80537736
🎯 Economical lag 141: correlation = 0.791784
🎯 Economical lag 142: correlation = 0.8151148
🎯 Economical lag 143: correlation = 0.834105
🎯 Economical lag 144: correlation = 0.8640579
🎯 Economical lag 145: correlation = 0.8319071
🎯 Economical lag 146: correlation = 0.8081003
🎯 Economical lag 147: correlation = 0.7989832
🎯 Economical lag 148: correlation = 0.7800583
🎯 Economical lag 149: correlation = 0.6635555
🎯 Economical lag 150: correlation = 0.6171185
🎯 Economical lag 151: correlation = 0.5964008
🎯 Economical lag 152: correlation = 0.53537196
🔍 Best correlation: 0.8640579 at period 144 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-9.259259 Hz (periods: 115-152)
📊 Moving averages updated - Period: 145.0, Amplitude: 0.8731529, RunLength: 2
✅ Pitch detected: 7.4074073 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.4074073 Hz
🎵 Detected pitch: 7.4074073 Hz (total samples: 369600)
� Calculated pressure: 3.629888 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.4074073, Pressure: 3.629888, Breaths: 1
🎤 Audio Level: 0.07777661, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 374400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.2248334e-05, Max: 0.0012790826, Avg: 0.00023438837, Variance: 4.3356085e-08
🎯 Economical search: one wavelength ahead 288, clamped 124, window [96, 152]
🎯 Economical lag 96: correlation = 0.5775101
🎯 Economical lag 97: correlation = 0.6692575
🎯 Economical lag 98: correlation = 0.7190004
🎯 Economical lag 99: correlation = 0.75033736
🎯 Economical lag 100: correlation = 0.77267486
🎯 Economical lag 101: correlation = 0.84961724
🎯 Economical lag 102: correlation = 0.860231
🎯 Economical lag 103: correlation = 0.86132854
🎯 Economical lag 104: correlation = 0.8204271
🎯 Economical lag 105: correlation = 0.8164582
🎯 Economical lag 106: correlation = 0.7635132
🎯 Economical lag 107: correlation = 0.74092436
🎯 Economical lag 108: correlation = 0.7446798
🎯 Economical lag 109: correlation = 0.7173065
🎯 Economical lag 110: correlation = 0.67389315
🎯 Economical lag 111: correlation = 0.6442897
🎯 Economical lag 112: correlation = 0.5700639
🎯 Economical lag 113: correlation = 0.57704926
🎯 Economical lag 114: correlation = 0.537737
🎯 Economical lag 115: correlation = 0.5045889
🎯 Economical lag 116: correlation = 0.48669246
🎯 Economical lag 117: correlation = 0.47565654
🎯 Economical lag 118: correlation = 0.42236945
🎯 Economical lag 119: correlation = 0.42172977
🎯 Economical lag 120: correlation = 0.40825906
🎯 Economical lag 121: correlation = 0.404261
🎯 Economical lag 122: correlation = 0.3897794
🎯 Economical lag 123: correlation = 0.3884366
🎯 Economical lag 124: correlation = 0.37803677
🎯 Economical lag 125: correlation = 0.3750701
🎯 Economical lag 126: correlation = 0.38677824
🎯 Economical lag 127: correlation = 0.42678857
🎯 Economical lag 128: correlation = 0.46955973
🎯 Economical lag 129: correlation = 0.5518843
🎯 Economical lag 130: correlation = 0.61465865
🎯 Economical lag 131: correlation = 0.6581334
🎯 Economical lag 132: correlation = 0.693088
🎯 Economical lag 133: correlation = 0.7742353
🎯 Economical lag 134: correlation = 0.8277366
🎯 Economical lag 135: correlation = 0.8605844
🎯 Economical lag 136: correlation = 0.8862811
🎯 Economical lag 137: correlation = 0.9046394
🎯 Economical lag 138: correlation = 0.8803611
🎯 Economical lag 139: correlation = 0.862782
🎯 Economical lag 140: correlation = 0.846423
🎯 Economical lag 141: correlation = 0.81535435
🎯 Economical lag 142: correlation = 0.77995265
🎯 Economical lag 143: correlation = 0.7311496
🎯 Economical lag 144: correlation = 0.68736887
🎯 Economical lag 145: correlation = 0.66414225
🎯 Economical lag 146: correlation = 0.61061543
🎯 Economical lag 147: correlation = 0.5682174
🎯 Economical lag 148: correlation = 0.5374848
🎯 Economical lag 149: correlation = 0.50481045
🎯 Economical lag 150: correlation = 0.44181585
🎯 Economical lag 151: correlation = 0.43099505
🎯 Economical lag 152: correlation = 0.42659375
🔍 Best correlation: 0.9046394 at period 137 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-9.73236 Hz (periods: 109-152)
📊 Moving averages updated - Period: 142.33334, Amplitude: 0.8836484, RunLength: 3
✅ Pitch detected: 7.7858877 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.7858877 Hz
🎵 Detected pitch: 7.7858877 Hz (total samples: 374400)
� Calculated pressure: 4.053408 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.7858877, Pressure: 4.053408, Breaths: 1
🎤 Audio Level: 0.069146916, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 379200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 6.9887487e-06, Max: 0.0014561297, Avg: 0.00024011936, Variance: 5.9810525e-08
🎯 Economical search: one wavelength ahead 274, clamped 125, window [98, 152]
🎯 Economical lag 98: correlation = 0.5255473
🎯 Economical lag 99: correlation = 0.55239725
🎯 Economical lag 100: correlation = 0.5946311
🎯 Economical lag 101: correlation = 0.6266795
🎯 Economical lag 102: correlation = 0.67683536
🎯 Economical lag 103: correlation = 0.77165663
🎯 Economical lag 104: correlation = 0.7948188
🎯 Economical lag 105: correlation = 0.798434
🎯 Economical lag 106: correlation = 0.7860046
🎯 Economical lag 107: correlation = 0.7325258
🎯 Economical lag 108: correlation = 0.74499595
🎯 Economical lag 109: correlation = 0.6877223
🎯 Economical lag 110: correlation = 0.60677236
🎯 Economical lag 111: correlation = 0.55986416
🎯 Economical lag 112: correlation = 0.5088127
🎯 Economical lag 113: correlation = 0.4528572
🎯 Economical lag 114: correlation = 0.42957863
🎯 Economical lag 115: correlation = 0.41346276
🎯 Economical lag 116: correlation = 0.38683763
🎯 Economical lag 117: correlation = 0.3623583
🎯 Economical lag 118: correlation = 0.35517928
🎯 Economical lag 119: correlation = 0.35238853
🎯 Economical lag 120: correlation = 0.3516442
🎯 Economical lag 121: correlation = 0.3732776
🎯 Economical lag 122: correlation = 0.3766883
🎯 Economical lag 123: correlation = 0.35926926
🎯 Economical lag 124: correlation = 0.34843376
🎯 Economical lag 125: correlation = 0.3360262
🎯 Economical lag 126: correlation = 0.3242342
🎯 Economical lag 127: correlation = 0.3207778
🎯 Economical lag 128: correlation = 0.31429636
🎯 Economical lag 129: correlation = 0.31140038
🎯 Economical lag 130: correlation = 0.32152292
🎯 Economical lag 131: correlation = 0.3470819
🎯 Economical lag 132: correlation = 0.393841
🎯 Economical lag 133: correlation = 0.44874534
🎯 Economical lag 134: correlation = 0.5033984
🎯 Economical lag 135: correlation = 0.54993695
🎯 Economical lag 136: correlation = 0.5642488
🎯 Economical lag 137: correlation = 0.5910975
🎯 Economical lag 138: correlation = 0.67369527
🎯 Economical lag 139: correlation = 0.78106534
🎯 Economical lag 140: correlation = 0.8219439
🎯 Economical lag 141: correlation = 0.7579307
🎯 Economical lag 142: correlation = 0.6938076
🎯 Economical lag 143: correlation = 0.67361003
🎯 Economical lag 144: correlation = 0.6275208
🎯 Economical lag 145: correlation = 0.56350636
🎯 Economical lag 146: correlation = 0.5156767
🎯 Economical lag 147: correlation = 0.475747
🎯 Economical lag 148: correlation = 0.41781032
🎯 Economical lag 149: correlation = 0.3560238
🎯 Economical lag 150: correlation = 0.34093717
🎯 Economical lag 151: correlation = 0.32289192
🎯 Economical lag 152: correlation = 0.3283341
🔍 Best correlation: 0.8219439 at period 140 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-9.523809 Hz (periods: 112-152)
📊 Moving averages updated - Period: 141.75, Amplitude: 0.86822224, RunLength: 4
✅ Pitch detected: 7.619047 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.619047 Hz
🎵 Detected pitch: 7.619047 Hz (total samples: 379200)
� Calculated pressure: 3.866714 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.619047, Pressure: 3.866714, Breaths: 1
🎤 Audio Level: 0.09037929, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 384000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 6.9887487e-06, Max: 0.0014561297, Avg: 0.0002455123, Variance: 6.467375e-08
🎯 Economical search: one wavelength ahead 280, clamped 124, window [96, 152]
🎯 Economical lag 96: correlation = 0.3955486
🎯 Economical lag 97: correlation = 0.4611801
🎯 Economical lag 98: correlation = 0.5258909
🎯 Economical lag 99: correlation = 0.6037499
🎯 Economical lag 100: correlation = 0.65424347
🎯 Economical lag 101: correlation = 0.6759432
🎯 Economical lag 102: correlation = 0.7159247
🎯 Economical lag 103: correlation = 0.7453151
🎯 Economical lag 104: correlation = 0.762745
🎯 Economical lag 105: correlation = 0.80343324
🎯 Economical lag 106: correlation = 0.75535965
🎯 Economical lag 107: correlation = 0.71205646
🎯 Economical lag 108: correlation = 0.67790323
🎯 Economical lag 109: correlation = 0.6265805
🎯 Economical lag 110: correlation = 0.6124644
🎯 Economical lag 111: correlation = 0.5513717
🎯 Economical lag 112: correlation = 0.4935564
🎯 Economical lag 113: correlation = 0.48588768
🎯 Economical lag 114: correlation = 0.4667413
🎯 Economical lag 115: correlation = 0.45147616
🎯 Economical lag 116: correlation = 0.44299418
🎯 Economical lag 117: correlation = 0.4292905
🎯 Economical lag 118: correlation = 0.43566704
🎯 Economical lag 119: correlation = 0.41914588
🎯 Economical lag 120: correlation = 0.4230673
🎯 Economical lag 121: correlation = 0.41610417
🎯 Economical lag 122: correlation = 0.38963255
🎯 Economical lag 123: correlation = 0.37471426
🎯 Economical lag 124: correlation = 0.36082953
🎯 Economical lag 125: correlation = 0.33870038
🎯 Economical lag 126: correlation = 0.3548782
🎯 Economical lag 127: correlation = 0.35764423
🎯 Economical lag 128: correlation = 0.3905441
🎯 Economical lag 129: correlation = 0.4416513
🎯 Economical lag 130: correlation = 0.50273365
🎯 Economical lag 131: correlation = 0.5564114
🎯 Economical lag 132: correlation = 0.6032926
🎯 Economical lag 133: correlation = 0.655115
🎯 Economical lag 134: correlation = 0.67115605
🎯 Economical lag 135: correlation = 0.7381548
🎯 Economical lag 136: correlation = 0.7847611
🎯 Economical lag 137: correlation = 0.83420235
🎯 Economical lag 138: correlation = 0.82450926
🎯 Economical lag 139: correlation = 0.8115996
🎯 Economical lag 140: correlation = 0.8065862
🎯 Economical lag 141: correlation = 0.80831146
🎯 Economical lag 142: correlation = 0.78077316
🎯 Economical lag 143: correlation = 0.76743376
🎯 Economical lag 144: correlation = 0.79541934
🎯 Economical lag 145: correlation = 0.7556615
🎯 Economical lag 146: correlation = 0.675055
🎯 Economical lag 147: correlation = 0.634332
🎯 Economical lag 148: correlation = 0.63044137
🎯 Economical lag 149: correlation = 0.53821576
🎯 Economical lag 150: correlation = 0.56762666
🎯 Economical lag 151: correlation = 0.53816605
🎯 Economical lag 152: correlation = 0.5413305
🔍 Best correlation: 0.83420235 at period 137 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-9.73236 Hz (periods: 109-152)
📊 Moving averages updated - Period: 140.8, Amplitude: 0.86141825, RunLength: 5
✅ Pitch detected: 7.7858877 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.7858877 Hz
🎵 Detected pitch: 7.7858877 Hz (total samples: 384000)
� Calculated pressure: 4.053408 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.7858877, Pressure: 4.053408, Breaths: 1
🎤 Audio Level: 0.08307229, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 388800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 2.2006403e-05, Max: 0.00097398955, Avg: 0.00024221068, Variance: 3.858112e-08
🎯 Economical search: one wavelength ahead 274, clamped 125, window [98, 152]
🎯 Economical lag 98: correlation = 0.6948439
🎯 Economical lag 99: correlation = 0.68473774
🎯 Economical lag 100: correlation = 0.6946902
🎯 Economical lag 101: correlation = 0.73523134
🎯 Economical lag 102: correlation = 0.79255646
🎯 Economical lag 103: correlation = 0.8322589
🎯 Economical lag 104: correlation = 0.8572617
🎯 Economical lag 105: correlation = 0.81197274
🎯 Economical lag 106: correlation = 0.7560724
🎯 Economical lag 107: correlation = 0.69755775
🎯 Economical lag 108: correlation = 0.69080824
🎯 Economical lag 109: correlation = 0.6705479
🎯 Economical lag 110: correlation = 0.6890954
🎯 Economical lag 111: correlation = 0.6666125
🎯 Economical lag 112: correlation = 0.62594724
🎯 Economical lag 113: correlation = 0.5578091
🎯 Economical lag 114: correlation = 0.53035766
🎯 Economical lag 115: correlation = 0.48410478
🎯 Economical lag 116: correlation = 0.48134163
🎯 Economical lag 117: correlation = 0.45356804
🎯 Economical lag 118: correlation = 0.43614933
🎯 Economical lag 119: correlation = 0.42827183
🎯 Economical lag 120: correlation = 0.41736066
🎯 Economical lag 121: correlation = 0.40844426
🎯 Economical lag 122: correlation = 0.39873892
🎯 Economical lag 123: correlation = 0.38354298
🎯 Economical lag 124: correlation = 0.378873
🎯 Economical lag 125: correlation = 0.4137241
🎯 Economical lag 126: correlation = 0.44423035
🎯 Economical lag 127: correlation = 0.50646865
🎯 Economical lag 128: correlation = 0.5170082
🎯 Economical lag 129: correlation = 0.5806131
🎯 Economical lag 130: correlation = 0.58810985
🎯 Economical lag 131: correlation = 0.6266665
🎯 Economical lag 132: correlation = 0.66788876
🎯 Economical lag 133: correlation = 0.72082996
🎯 Economical lag 134: correlation = 0.7531631
🎯 Economical lag 135: correlation = 0.766811
🎯 Economical lag 136: correlation = 0.77212614
🎯 Economical lag 137: correlation = 0.8260249
🎯 Economical lag 138: correlation = 0.8181362
🎯 Economical lag 139: correlation = 0.793424
🎯 Economical lag 140: correlation = 0.7843035
🎯 Economical lag 141: correlation = 0.81857646
🎯 Economical lag 142: correlation = 0.7995306
🎯 Economical lag 143: correlation = 0.7615543
🎯 Economical lag 144: correlation = 0.7429444
🎯 Economical lag 145: correlation = 0.7109595
🎯 Economical lag 146: correlation = 0.6953507
🎯 Economical lag 147: correlation = 0.67146486
🎯 Economical lag 148: correlation = 0.63257307
🎯 Economical lag 149: correlation = 0.5544981
🎯 Economical lag 150: correlation = 0.5312812
🎯 Economical lag 151: correlation = 0.44774505
🎯 Economical lag 152: correlation = 0.46053064
🔍 Best correlation: 0.8572617 at period 104 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.6923075-12.820512 Hz (periods: 83-138)
🔄 Run length reduced: 5 → 4 (leap: 2.6806526 Hz, threshold: 1.5151514 Hz)
📊 Moving averages updated - Period: 133.44, Amplitude: 0.86058694, RunLength: 5
✅ Pitch detected: 10.25641 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 10.25641 Hz
🎵 Detected pitch: 10.25641 Hz (total samples: 388800)
� Calculated pressure: 6.817922 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 10.25641, Pressure: 6.817922, Breaths: 1
🎤 Audio Level: 0.08181307, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 393600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.1581897e-05, Max: 0.0016333292, Avg: 0.0002700996, Variance: 6.437648e-08
🎯 Economical search: one wavelength ahead 208, clamped 132, window [112, 152]
🎯 Economical lag 112: correlation = 0.6616132
🎯 Economical lag 113: correlation = 0.64634156
🎯 Economical lag 114: correlation = 0.61634845
🎯 Economical lag 115: correlation = 0.5296277
🎯 Economical lag 116: correlation = 0.4741832
🎯 Economical lag 117: correlation = 0.4512254
🎯 Economical lag 118: correlation = 0.41892722
🎯 Economical lag 119: correlation = 0.38455197
🎯 Economical lag 120: correlation = 0.3634808
🎯 Economical lag 121: correlation = 0.36762834
🎯 Economical lag 122: correlation = 0.34944183
🎯 Economical lag 123: correlation = 0.35702696
🎯 Economical lag 124: correlation = 0.35275587
🎯 Economical lag 125: correlation = 0.3780588
🎯 Economical lag 126: correlation = 0.39172944
🎯 Economical lag 127: correlation = 0.39911252
🎯 Economical lag 128: correlation = 0.4247343
🎯 Economical lag 129: correlation = 0.45070314
🎯 Economical lag 130: correlation = 0.43828702
🎯 Economical lag 131: correlation = 0.45402917
🎯 Economical lag 132: correlation = 0.4727002
🎯 Economical lag 133: correlation = 0.5003109
🎯 Economical lag 134: correlation = 0.51243705
🎯 Economical lag 135: correlation = 0.5357297
🎯 Economical lag 136: correlation = 0.62606007
🎯 Economical lag 137: correlation = 0.68432826
🎯 Economical lag 138: correlation = 0.766021
🎯 Economical lag 139: correlation = 0.81429243
🎯 Economical lag 140: correlation = 0.8663796
🎯 Economical lag 141: correlation = 0.8579889
🎯 Economical lag 142: correlation = 0.8773128
🎯 Economical lag 143: correlation = 0.90039265
🎯 Economical lag 144: correlation = 0.8945834
🎯 Economical lag 145: correlation = 0.8660349
🎯 Economical lag 146: correlation = 0.84860194
🎯 Economical lag 147: correlation = 0.83099633
🎯 Economical lag 148: correlation = 0.79208267
🎯 Economical lag 149: correlation = 0.719736
🎯 Economical lag 150: correlation = 0.7078345
🎯 Economical lag 151: correlation = 0.68205816
🎯 Economical lag 152: correlation = 0.62852126
🔍 Best correlation: 0.90039265 at period 143 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-9.324009 Hz (periods: 114-152)
📊 Moving averages updated - Period: 135.352, Amplitude: 0.8685481, RunLength: 5
✅ Pitch detected: 7.459207 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.459207 Hz
🎵 Detected pitch: 7.459207 Hz (total samples: 393600)
� Calculated pressure: 3.6878524 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.459207, Pressure: 3.6878524, Breaths: 1
🎤 Audio Level: 0.06556092, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 398400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.1240114e-05, Max: 0.0016333292, Avg: 0.00024913414, Variance: 6.1451544e-08
🎯 Economical search: one wavelength ahead 286, clamped 124, window [96, 152]
🎯 Economical lag 96: correlation = 0.53833234
🎯 Economical lag 97: correlation = 0.5856545
🎯 Economical lag 98: correlation = 0.60891336
🎯 Economical lag 99: correlation = 0.6428754
🎯 Economical lag 100: correlation = 0.70272
🎯 Economical lag 101: correlation = 0.7540599
🎯 Economical lag 102: correlation = 0.8038845
🎯 Economical lag 103: correlation = 0.8497101
🎯 Economical lag 104: correlation = 0.8592935
🎯 Economical lag 105: correlation = 0.86258346
🎯 Economical lag 106: correlation = 0.81857324
🎯 Economical lag 107: correlation = 0.74788666
🎯 Economical lag 108: correlation = 0.65707844
🎯 Economical lag 109: correlation = 0.6149666
🎯 Economical lag 110: correlation = 0.58324105
🎯 Economical lag 111: correlation = 0.5118658
🎯 Economical lag 112: correlation = 0.48073384
🎯 Economical lag 113: correlation = 0.45986223
🎯 Economical lag 114: correlation = 0.4137917
🎯 Economical lag 115: correlation = 0.38521224
🎯 Economical lag 116: correlation = 0.35484546
🎯 Economical lag 117: correlation = 0.33941862
🎯 Economical lag 118: correlation = 0.3322599
🎯 Economical lag 119: correlation = 0.31995592
🎯 Economical lag 120: correlation = 0.31203598
🎯 Economical lag 121: correlation = 0.30934086
🎯 Economical lag 122: correlation = 0.3196069
🎯 Economical lag 123: correlation = 0.32836494
🎯 Economical lag 124: correlation = 0.34614515
🎯 Economical lag 125: correlation = 0.36581483
🎯 Economical lag 126: correlation = 0.39086837
🎯 Economical lag 127: correlation = 0.41225135
🎯 Economical lag 128: correlation = 0.45143318
🎯 Economical lag 129: correlation = 0.4847856
🎯 Economical lag 130: correlation = 0.5320605
🎯 Economical lag 131: correlation = 0.58425665
🎯 Economical lag 132: correlation = 0.6511243
🎯 Economical lag 133: correlation = 0.7299867
🎯 Economical lag 134: correlation = 0.73565364
🎯 Economical lag 135: correlation = 0.7309827
🎯 Economical lag 136: correlation = 0.77519834
🎯 Economical lag 137: correlation = 0.81450117
🎯 Economical lag 138: correlation = 0.78872305
🎯 Economical lag 139: correlation = 0.8023229
🎯 Economical lag 140: correlation = 0.7844867
🎯 Economical lag 141: correlation = 0.68825877
🎯 Economical lag 142: correlation = 0.6434307
🎯 Economical lag 143: correlation = 0.68349457
🎯 Economical lag 144: correlation = 0.6910106
🎯 Economical lag 145: correlation = 0.6537944
🎯 Economical lag 146: correlation = 0.5842264
🎯 Economical lag 147: correlation = 0.54143
🎯 Economical lag 148: correlation = 0.4841285
🎯 Economical lag 149: correlation = 0.4302357
🎯 Economical lag 150: correlation = 0.4143235
🎯 Economical lag 151: correlation = 0.38382238
🎯 Economical lag 152: correlation = 0.34892464
🔍 Best correlation: 0.86258346 at period 105 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.619047-12.698412 Hz (periods: 84-140)
🔄 Run length reduced: 5 → 4 (leap: 2.2780437 Hz, threshold: 1.5761372 Hz)
📊 Moving averages updated - Period: 129.2816, Amplitude: 0.86735517, RunLength: 5
✅ Pitch detected: 10.15873 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 10.15873 Hz
🎵 Detected pitch: 10.15873 Hz (total samples: 398400)
� Calculated pressure: 6.7086177 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 10.15873, Pressure: 6.7086177, Breaths: 1
🎤 Audio Level: 0.07696262, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 403200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.1240114e-05, Max: 0.0009899231, Avg: 0.00022809481, Variance: 4.2758774e-08
🎯 Economical search: one wavelength ahead 210, clamped 131, window [110, 152]
🎯 Economical lag 110: correlation = 0.77765167
🎯 Economical lag 111: correlation = 0.79230005
🎯 Economical lag 112: correlation = 0.7046833
🎯 Economical lag 113: correlation = 0.6555173
🎯 Economical lag 114: correlation = 0.6111157
🎯 Economical lag 115: correlation = 0.5581681
🎯 Economical lag 116: correlation = 0.5075542
🎯 Economical lag 117: correlation = 0.5142907
🎯 Economical lag 118: correlation = 0.5002872
🎯 Economical lag 119: correlation = 0.48717174
🎯 Economical lag 120: correlation = 0.42688566
🎯 Economical lag 121: correlation = 0.4305624
🎯 Economical lag 122: correlation = 0.40108085
🎯 Economical lag 123: correlation = 0.37801677
🎯 Economical lag 124: correlation = 0.34917393
🎯 Economical lag 125: correlation = 0.36579558
🎯 Economical lag 126: correlation = 0.37809363
🎯 Economical lag 127: correlation = 0.38940647
🎯 Economical lag 128: correlation = 0.38312897
🎯 Economical lag 129: correlation = 0.35863224
🎯 Economical lag 130: correlation = 0.36143538
🎯 Economical lag 131: correlation = 0.36957467
🎯 Economical lag 132: correlation = 0.403539
🎯 Economical lag 133: correlation = 0.47195807
🎯 Economical lag 134: correlation = 0.52562886
🎯 Economical lag 135: correlation = 0.5824529
🎯 Economical lag 136: correlation = 0.6362346
🎯 Economical lag 137: correlation = 0.6707166
🎯 Economical lag 138: correlation = 0.6840915
🎯 Economical lag 139: correlation = 0.71496135
🎯 Economical lag 140: correlation = 0.70856225
🎯 Economical lag 141: correlation = 0.7215019
🎯 Economical lag 142: correlation = 0.73959583
🎯 Economical lag 143: correlation = 0.7551262
🎯 Economical lag 144: correlation = 0.7665987
🎯 Economical lag 145: correlation = 0.7813256
🎯 Economical lag 146: correlation = 0.77106184
🎯 Economical lag 147: correlation = 0.70238405
🎯 Economical lag 148: correlation = 0.6553304
🎯 Economical lag 149: correlation = 0.5991666
🎯 Economical lag 150: correlation = 0.5809088
🎯 Economical lag 151: correlation = 0.5462806
🎯 Economical lag 152: correlation = 0.5356106
🔍 Best correlation: 0.79230005 at period 111 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.207207-12.0120125 Hz (periods: 88-148)
📊 Moving averages updated - Period: 125.62528, Amplitude: 0.85234416, RunLength: 5
✅ Pitch detected: 9.60961 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.60961 Hz
🎵 Detected pitch: 9.60961 Hz (total samples: 403200)
� Calculated pressure: 6.094153 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.60961, Pressure: 6.094153, Breaths: 1
🎤 Audio Level: 0.05610206, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 408000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.3216194e-05, Max: 0.0009899231, Avg: 0.00022581108, Variance: 3.728937e-08
🎯 Economical search: one wavelength ahead 222, clamped 130, window [108, 152]
🎯 Economical lag 108: correlation = 0.71777964
🎯 Economical lag 109: correlation = 0.740154
🎯 Economical lag 110: correlation = 0.8123101
🎯 Economical lag 111: correlation = 0.85575294
🎯 Economical lag 112: correlation = 0.8432983
🎯 Economical lag 113: correlation = 0.8081352
🎯 Economical lag 114: correlation = 0.7542622
🎯 Economical lag 115: correlation = 0.73673785
🎯 Economical lag 116: correlation = 0.69979864
🎯 Economical lag 117: correlation = 0.6332609
🎯 Economical lag 118: correlation = 0.58804965
🎯 Economical lag 119: correlation = 0.5636897
🎯 Economical lag 120: correlation = 0.54318005
🎯 Economical lag 121: correlation = 0.53935397
🎯 Economical lag 122: correlation = 0.52711153
🎯 Economical lag 123: correlation = 0.4874779
🎯 Economical lag 124: correlation = 0.479748
🎯 Economical lag 125: correlation = 0.4588554
🎯 Economical lag 126: correlation = 0.44253564
🎯 Economical lag 127: correlation = 0.4112654
🎯 Economical lag 128: correlation = 0.40879413
🎯 Economical lag 129: correlation = 0.41768333
🎯 Economical lag 130: correlation = 0.45548376
🎯 Economical lag 131: correlation = 0.4542503
🎯 Economical lag 132: correlation = 0.45113063
🎯 Economical lag 133: correlation = 0.4461536
🎯 Economical lag 134: correlation = 0.45514894
🎯 Economical lag 135: correlation = 0.4422092
🎯 Economical lag 136: correlation = 0.46049723
🎯 Economical lag 137: correlation = 0.45365095
🎯 Economical lag 138: correlation = 0.49876878
🎯 Economical lag 139: correlation = 0.5534625
🎯 Economical lag 140: correlation = 0.5489259
🎯 Economical lag 141: correlation = 0.6012509
🎯 Economical lag 142: correlation = 0.6339047
🎯 Economical lag 143: correlation = 0.7063664
🎯 Economical lag 144: correlation = 0.72254694
🎯 Economical lag 145: correlation = 0.7427932
🎯 Economical lag 146: correlation = 0.73834234
🎯 Economical lag 147: correlation = 0.75971186
🎯 Economical lag 148: correlation = 0.7565445
🎯 Economical lag 149: correlation = 0.762892
🎯 Economical lag 150: correlation = 0.78074926
🎯 Economical lag 151: correlation = 0.8113304
🎯 Economical lag 152: correlation = 0.77270025
🔍 Best correlation: 0.85575294 at period 111 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.207207-12.0120125 Hz (periods: 88-148)
📊 Moving averages updated - Period: 122.700226, Amplitude: 0.8530259, RunLength: 5
✅ Pitch detected: 9.60961 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.60961 Hz
🎵 Detected pitch: 9.60961 Hz (total samples: 408000)
� Calculated pressure: 6.094153 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.60961, Pressure: 6.094153, Breaths: 1
🎤 Audio Level: 0.07701698, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 412800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.4874958e-05, Max: 0.0007965419, Avg: 0.00021339132, Variance: 3.0894352e-08
🎯 Economical search: one wavelength ahead 222, clamped 130, window [108, 152]
🎯 Economical lag 108: correlation = 0.7549699
🎯 Economical lag 109: correlation = 0.8134752
🎯 Economical lag 110: correlation = 0.8541654
🎯 Economical lag 111: correlation = 0.84565777
🎯 Economical lag 112: correlation = 0.86588186
🎯 Economical lag 113: correlation = 0.85286224
🎯 Economical lag 114: correlation = 0.81408757
🎯 Economical lag 115: correlation = 0.80456716
🎯 Economical lag 116: correlation = 0.7439447
🎯 Economical lag 117: correlation = 0.720521
🎯 Economical lag 118: correlation = 0.69807523
🎯 Economical lag 119: correlation = 0.68926406
🎯 Economical lag 120: correlation = 0.6753439
🎯 Economical lag 121: correlation = 0.66244406
🎯 Economical lag 122: correlation = 0.6107762
🎯 Economical lag 123: correlation = 0.5497625
🎯 Economical lag 124: correlation = 0.5141488
🎯 Economical lag 125: correlation = 0.47646487
🎯 Economical lag 126: correlation = 0.45386353
🎯 Economical lag 127: correlation = 0.4321214
🎯 Economical lag 128: correlation = 0.38760155
🎯 Economical lag 129: correlation = 0.38551766
🎯 Economical lag 130: correlation = 0.37063277
🎯 Economical lag 131: correlation = 0.35663942
🎯 Economical lag 132: correlation = 0.36161172
🎯 Economical lag 133: correlation = 0.3618729
🎯 Economical lag 134: correlation = 0.3986175
🎯 Economical lag 135: correlation = 0.42163306
🎯 Economical lag 136: correlation = 0.43861338
🎯 Economical lag 137: correlation = 0.46385807
🎯 Economical lag 138: correlation = 0.4879703
🎯 Economical lag 139: correlation = 0.5041582
🎯 Economical lag 140: correlation = 0.5251931
🎯 Economical lag 141: correlation = 0.5495935
🎯 Economical lag 142: correlation = 0.62259996
🎯 Economical lag 143: correlation = 0.6809352
🎯 Economical lag 144: correlation = 0.7003933
🎯 Economical lag 145: correlation = 0.78368306
🎯 Economical lag 146: correlation = 0.8143833
🎯 Economical lag 147: correlation = 0.79729617
🎯 Economical lag 148: correlation = 0.82184446
🎯 Economical lag 149: correlation = 0.84785175
🎯 Economical lag 150: correlation = 0.8301218
🎯 Economical lag 151: correlation = 0.7959189
🎯 Economical lag 152: correlation = 0.783687
🔍 Best correlation: 0.86588186 at period 112 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.142857-11.904762 Hz (periods: 89-149)
📊 Moving averages updated - Period: 120.56018, Amplitude: 0.8555971, RunLength: 5
✅ Pitch detected: 9.523809 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.523809 Hz
🎵 Detected pitch: 9.523809 Hz (total samples: 412800)
� Calculated pressure: 5.9981427 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🫁 Breath detection: Breath now active (duration: 1.5s)
🖥️ UI updated - Freq: 9.523809, Pressure: 5.9981427, Breaths: 1
🎤 Audio Level: 0.07299779, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 417600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 1.1193783e-05, Max: 0.0009138123, Avg: 0.00021310558, Variance: 3.2725545e-08
🎯 Economical search: one wavelength ahead 224, clamped 130, window [108, 152]
🎯 Economical lag 108: correlation = 0.7045559
🎯 Economical lag 109: correlation = 0.7447795
🎯 Economical lag 110: correlation = 0.772076
🎯 Economical lag 111: correlation = 0.7779078
🎯 Economical lag 112: correlation = 0.8118578
🎯 Economical lag 113: correlation = 0.81961656
🎯 Economical lag 114: correlation = 0.8372276
🎯 Economical lag 115: correlation = 0.8407115
🎯 Economical lag 116: correlation = 0.82598364
🎯 Economical lag 117: correlation = 0.8086741
🎯 Economical lag 118: correlation = 0.80417514
🎯 Economical lag 119: correlation = 0.7718256
🎯 Economical lag 120: correlation = 0.6901327
🎯 Economical lag 121: correlation = 0.6191984
🎯 Economical lag 122: correlation = 0.6012678
🎯 Economical lag 123: correlation = 0.5645744
🎯 Economical lag 124: correlation = 0.51117754
🎯 Economical lag 125: correlation = 0.49572185
🎯 Economical lag 126: correlation = 0.4847808
🎯 Economical lag 127: correlation = 0.4697157
🎯 Economical lag 128: correlation = 0.43334714
🎯 Economical lag 129: correlation = 0.40952736
🎯 Economical lag 130: correlation = 0.40443316
🎯 Economical lag 131: correlation = 0.41629133
🎯 Economical lag 132: correlation = 0.42469904
🎯 Economical lag 133: correlation = 0.39848718
🎯 Economical lag 134: correlation = 0.40024033
🎯 Economical lag 135: correlation = 0.3841487
🎯 Economical lag 136: correlation = 0.36533087
🎯 Economical lag 137: correlation = 0.37318918
🎯 Economical lag 138: correlation = 0.36985266
🎯 Economical lag 139: correlation = 0.394132
🎯 Economical lag 140: correlation = 0.4358079
🎯 Economical lag 141: correlation = 0.46786404
🎯 Economical lag 142: correlation = 0.52240235
🎯 Economical lag 143: correlation = 0.5612492
🎯 Economical lag 144: correlation = 0.60081995
🎯 Economical lag 145: correlation = 0.63460183
🎯 Economical lag 146: correlation = 0.6440851
🎯 Economical lag 147: correlation = 0.6684624
🎯 Economical lag 148: correlation = 0.7021862
🎯 Economical lag 149: correlation = 0.7421723
🎯 Economical lag 150: correlation = 0.8052099
🎯 Economical lag 151: correlation = 0.83908963
🎯 Economical lag 152: correlation = 0.87480867
🔍 Best correlation: 0.87480867 at period 152 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-8.77193 Hz (periods: 121-152)
🔄 Run length reduced: 5 → 4 (leap: 1.8300428 Hz, threshold: 1.7695173 Hz)
📊 Moving averages updated - Period: 126.848145, Amplitude: 0.85943943, RunLength: 5
✅ Pitch detected: 7.017544 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.017544 Hz
🎵 Detected pitch: 7.017544 Hz (total samples: 417600)
� Calculated pressure: 3.1936312 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.017544, Pressure: 3.1936312, Breaths: 1
🎤 Audio Level: 0.067790404, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 422400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 9.252583e-06, Max: 0.0009138123, Avg: 0.00022335084, Variance: 3.739596e-08
🎯 Economical search: one wavelength ahead 304, clamped 122, window [92, 152]
🎯 Economical lag 92: correlation = 0.38336584
🎯 Economical lag 93: correlation = 0.3744931
🎯 Economical lag 94: correlation = 0.38742518
🎯 Economical lag 95: correlation = 0.41856384
🎯 Economical lag 96: correlation = 0.4353797
🎯 Economical lag 97: correlation = 0.4296711
🎯 Economical lag 98: correlation = 0.43574634
🎯 Economical lag 99: correlation = 0.45527735
🎯 Economical lag 100: correlation = 0.44586888
🎯 Economical lag 101: correlation = 0.4613521
🎯 Economical lag 102: correlation = 0.49493876
🎯 Economical lag 103: correlation = 0.5103078
🎯 Economical lag 104: correlation = 0.55687934
🎯 Economical lag 105: correlation = 0.5589523
🎯 Economical lag 106: correlation = 0.5820785
🎯 Economical lag 107: correlation = 0.5997471
🎯 Economical lag 108: correlation = 0.63877034
🎯 Economical lag 109: correlation = 0.65398145
🎯 Economical lag 110: correlation = 0.6695324
🎯 Economical lag 111: correlation = 0.69462645
🎯 Economical lag 112: correlation = 0.7477639
🎯 Economical lag 113: correlation = 0.7756866
🎯 Economical lag 114: correlation = 0.8130051
🎯 Economical lag 115: correlation = 0.8246109
🎯 Economical lag 116: correlation = 0.82074195
🎯 Economical lag 117: correlation = 0.8158838
🎯 Economical lag 118: correlation = 0.79563564
🎯 Economical lag 119: correlation = 0.75428444
🎯 Economical lag 120: correlation = 0.7155955
🎯 Economical lag 121: correlation = 0.6829259
🎯 Economical lag 122: correlation = 0.6570811
🎯 Economical lag 123: correlation = 0.59811735
🎯 Economical lag 124: correlation = 0.5610348
🎯 Economical lag 125: correlation = 0.51956713
🎯 Economical lag 126: correlation = 0.48243442
🎯 Economical lag 127: correlation = 0.4906291
🎯 Economical lag 128: correlation = 0.44890788
🎯 Economical lag 129: correlation = 0.44573808
🎯 Economical lag 130: correlation = 0.45175794
🎯 Economical lag 131: correlation = 0.4516566
🎯 Economical lag 132: correlation = 0.4573643
🎯 Economical lag 133: correlation = 0.4441718
🎯 Economical lag 134: correlation = 0.4435956
🎯 Economical lag 135: correlation = 0.43448868
🎯 Economical lag 136: correlation = 0.43435958
🎯 Economical lag 137: correlation = 0.43990105
🎯 Economical lag 138: correlation = 0.44404846
🎯 Economical lag 139: correlation = 0.49306348
🎯 Economical lag 140: correlation = 0.4926094
🎯 Economical lag 141: correlation = 0.48930672
🎯 Economical lag 142: correlation = 0.531835
🎯 Economical lag 143: correlation = 0.5481445
🎯 Economical lag 144: correlation = 0.57704896
🎯 Economical lag 145: correlation = 0.6112094
🎯 Economical lag 146: correlation = 0.6535591
🎯 Economical lag 147: correlation = 0.6692372
🎯 Economical lag 148: correlation = 0.71657085
🎯 Economical lag 149: correlation = 0.7607752
🎯 Economical lag 150: correlation = 0.78757995
🎯 Economical lag 151: correlation = 0.76477635
🎯 Economical lag 152: correlation = 0.77175605
🔍 Best correlation: 0.8246109 at period 115 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-11.594202 Hz (periods: 92-152)
📊 Moving averages updated - Period: 124.478516, Amplitude: 0.85247374, RunLength: 5
✅ Pitch detected: 9.275362 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.275362 Hz
🎵 Detected pitch: 9.275362 Hz (total samples: 422400)
� Calculated pressure: 5.7201295 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.275362, Pressure: 5.7201295, Breaths: 1
🎤 Audio Level: 0.06418551, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 427200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 9.252583e-06, Max: 0.0010903514, Avg: 0.00021661028, Variance: 4.5254804e-08
🎯 Economical search: one wavelength ahead 230, clamped 129, window [106, 152]
🎯 Economical lag 106: correlation = 0.6129723
🎯 Economical lag 107: correlation = 0.67717284
🎯 Economical lag 108: correlation = 0.7388073
🎯 Economical lag 109: correlation = 0.77585155
🎯 Economical lag 110: correlation = 0.8245769
🎯 Economical lag 111: correlation = 0.8386733
🎯 Economical lag 112: correlation = 0.8307415
🎯 Economical lag 113: correlation = 0.8284228
🎯 Economical lag 114: correlation = 0.7699313
🎯 Economical lag 115: correlation = 0.7483894
🎯 Economical lag 116: correlation = 0.68882746
🎯 Economical lag 117: correlation = 0.6403494
🎯 Economical lag 118: correlation = 0.64206845
🎯 Economical lag 119: correlation = 0.589084
🎯 Economical lag 120: correlation = 0.54289234
🎯 Economical lag 121: correlation = 0.48917443
🎯 Economical lag 122: correlation = 0.46128976
🎯 Economical lag 123: correlation = 0.3950576
🎯 Economical lag 124: correlation = 0.37898338
🎯 Economical lag 125: correlation = 0.37450486
🎯 Economical lag 126: correlation = 0.34769174
🎯 Economical lag 127: correlation = 0.33891928
🎯 Economical lag 128: correlation = 0.33714363
🎯 Economical lag 129: correlation = 0.3043631
🎯 Economical lag 130: correlation = 0.3011004
🎯 Economical lag 135: correlation = 0.30693993
🎯 Economical lag 136: correlation = 0.33072087
🎯 Economical lag 137: correlation = 0.34464377
🎯 Economical lag 138: correlation = 0.3923653
🎯 Economical lag 139: correlation = 0.4267204
🎯 Economical lag 140: correlation = 0.44500694
🎯 Economical lag 141: correlation = 0.47828266
🎯 Economical lag 142: correlation = 0.50938565
🎯 Economical lag 143: correlation = 0.5444543
🎯 Economical lag 144: correlation = 0.60461044
🎯 Economical lag 145: correlation = 0.63675326
🎯 Economical lag 146: correlation = 0.70540726
🎯 Economical lag 147: correlation = 0.77867794
🎯 Economical lag 148: correlation = 0.8186675
🎯 Economical lag 149: correlation = 0.7959295
🎯 Economical lag 150: correlation = 0.7895923
🎯 Economical lag 151: correlation = 0.7603527
🎯 Economical lag 152: correlation = 0.7387957
🔍 Best correlation: 0.8386733 at period 111 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.207207-12.0120125 Hz (periods: 88-148)
📊 Moving averages updated - Period: 121.782814, Amplitude: 0.8497137, RunLength: 5
✅ Pitch detected: 9.60961 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.60961 Hz
🎵 Detected pitch: 9.60961 Hz (total samples: 427200)
� Calculated pressure: 6.094153 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.60961, Pressure: 6.094153, Breaths: 1
🎤 Audio Level: 0.061278947, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 432000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 7.714124e-06, Max: 0.0010903514, Avg: 0.00018765144, Variance: 3.7622932e-08
🎯 Economical search: one wavelength ahead 222, clamped 130, window [108, 152]
🎯 Economical lag 108: correlation = 0.33851334
🎯 Economical lag 109: correlation = 0.34917837
🎯 Economical lag 110: correlation = 0.37423712
🎯 Economical lag 111: correlation = 0.40486068
🎯 Economical lag 112: correlation = 0.44960934
🎯 Economical lag 113: correlation = 0.49273458
🎯 Economical lag 114: correlation = 0.55001956
🎯 Economical lag 115: correlation = 0.6004969
🎯 Economical lag 116: correlation = 0.679099
🎯 Economical lag 117: correlation = 0.74627686
🎯 Economical lag 118: correlation = 0.803838
🎯 Economical lag 119: correlation = 0.8386386
🎯 Economical lag 120: correlation = 0.8967284
🎯 Economical lag 121: correlation = 0.90460306
🎯 Economical lag 122: correlation = 0.9089301
🎯 Economical lag 123: correlation = 0.8130467
🎯 Economical lag 124: correlation = 0.7619979
🎯 Economical lag 125: correlation = 0.7041773
🎯 Economical lag 126: correlation = 0.644246
🎯 Economical lag 127: correlation = 0.5711106
🎯 Economical lag 128: correlation = 0.5259812
🎯 Economical lag 129: correlation = 0.46650577
🎯 Economical lag 130: correlation = 0.42121798
🎯 Economical lag 131: correlation = 0.37973797
🎯 Economical lag 132: correlation = 0.3406015
🎯 Economical lag 133: correlation = 0.3099252
🎯 Economical lag 150: correlation = 0.31613237
🎯 Economical lag 151: correlation = 0.37212265
🎯 Economical lag 152: correlation = 0.43555576
🔍 Best correlation: 0.9089301 at period 122 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-10.928961 Hz (periods: 97-152)
📊 Moving averages updated - Period: 121.82625, Amplitude: 0.86155695, RunLength: 5
✅ Pitch detected: 8.743169 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 8.743169 Hz
🎵 Detected pitch: 8.743169 Hz (total samples: 432000)
� Calculated pressure: 5.1246057 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 8.743169, Pressure: 5.1246057, Breaths: 1
🎤 Audio Level: 0.047326114, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 436800
🛑 stopRecording called
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 7.714124e-06, Max: 0.00070401886, Avg: 0.0001541058, Variance: 1.983455e-08
🎯 Economical search: one wavelength ahead 244, clamped 128, window [104, 152]
🎯 Economical lag 104: correlation = 0.38103303
🎯 Economical lag 105: correlation = 0.38279933
🎯 Economical lag 106: correlation = 0.40416262
🎯 Economical lag 107: correlation = 0.42434815
🎯 Economical lag 108: correlation = 0.44467765
🎯 Economical lag 109: correlation = 0.48667312
🎯 Economical lag 110: correlation = 0.53277874
🎯 Economical lag 111: correlation = 0.59440964
🎯 Economical lag 112: correlation = 0.6748204
🎯 Economical lag 113: correlation = 0.71983504
🎯 Economical lag 114: correlation = 0.7603198
🎯 Economical lag 115: correlation = 0.78800577
🎯 Economical lag 116: correlation = 0.83309555
🎯 Economical lag 117: correlation = 0.85283244
🎯 Economical lag 118: correlation = 0.8557094
🎯 Economical lag 119: correlation = 0.8402699
🎯 Economical lag 120: correlation = 0.8533441
🎯 Economical lag 121: correlation = 0.82486695
🎯 Economical lag 122: correlation = 0.8152194
🎯 Economical lag 123: correlation = 0.75873864
🎯 Economical lag 124: correlation = 0.717438
🎯 Economical lag 125: correlation = 0.7208814
🎯 Economical lag 126: correlation = 0.6767127
🎯 Economical lag 127: correlation = 0.6369964
🎯 Economical lag 128: correlation = 0.5875892
🎯 Economical lag 129: correlation = 0.5548242
🎯 Economical lag 130: correlation = 0.535727
🎯 Economical lag 131: correlation = 0.49948236
🎯 Economical lag 132: correlation = 0.46670178
🎯 Economical lag 133: correlation = 0.452541
🎯 Economical lag 134: correlation = 0.42294675
🎯 Economical lag 135: correlation = 0.41625267
🎯 Economical lag 136: correlation = 0.4016592
🎯 Economical lag 137: correlation = 0.40486428
🎯 Economical lag 138: correlation = 0.39043152
🎯 Economical lag 139: correlation = 0.3745039
🎯 Economical lag 140: correlation = 0.37203532
🎯 Economical lag 141: correlation = 0.35573706
🎯 Economical lag 142: correlation = 0.33266848
🎯 Economical lag 143: correlation = 0.3162287
🎯 Economical lag 144: correlation = 0.3066369
🎯 Economical lag 145: correlation = 0.3306432
🎯 Economical lag 146: correlation = 0.3511909
🎯 Economical lag 147: correlation = 0.3983785
🎯 Economical lag 148: correlation = 0.42845595
🎯 Economical lag 149: correlation = 0.47965038
🎯 Economical lag 150: correlation = 0.5747328
🎯 Economical lag 151: correlation = 0.6428695
🎯 Economical lag 152: correlation = 0.72173977
🔍 Best correlation: 0.8557094 at period 118 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-11.299435 Hz (periods: 94-152)
📊 Moving averages updated - Period: 121.061, Amplitude: 0.86038744, RunLength: 5
✅ Pitch detected: 9.039548 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.039548 Hz
🎵 Detected pitch: 9.039548 Hz (total samples: 436800)
� Calculated pressure: 5.4562535 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.055202655, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 441600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 106
🔧 Gaussian smoothing - sigma: 0.0075, kernelSize: 7
🔧 Step 4 complete - Smoothed samples: 106
🔧 Buffer length: 200 (target: 200)
🔍 Autocorrelation - Data length: 200, MinPeriod: 26, MaxPeriod: 152
🔍 Data stats - Min: 3.071978e-06, Max: 0.00047369397, Avg: 0.00011444507, Variance: 8.637844e-09
🎯 Economical search: one wavelength ahead 236, clamped 129, window [106, 152]
🎯 Economical lag 106: correlation = 0.46558794
🎯 Economical lag 107: correlation = 0.4498431
🎯 Economical lag 108: correlation = 0.45842835
🎯 Economical lag 109: correlation = 0.451231
🎯 Economical lag 110: correlation = 0.4413706
🎯 Economical lag 111: correlation = 0.4515555
🎯 Economical lag 112: correlation = 0.46523756
🎯 Economical lag 113: correlation = 0.4483396
🎯 Economical lag 114: correlation = 0.44480076
🎯 Economical lag 115: correlation = 0.46813166
🎯 Economical lag 116: correlation = 0.5122368
🎯 Economical lag 117: correlation = 0.5484622
🎯 Economical lag 118: correlation = 0.5724221
🎯 Economical lag 119: correlation = 0.5834696
🎯 Economical lag 120: correlation = 0.6159778
🎯 Economical lag 121: correlation = 0.6767345
🎯 Economical lag 122: correlation = 0.665312
🎯 Economical lag 123: correlation = 0.6512999
🎯 Economical lag 124: correlation = 0.65697086
🎯 Economical lag 125: correlation = 0.6986269
🎯 Economical lag 126: correlation = 0.754637
🎯 Economical lag 127: correlation = 0.7687314
🎯 Economical lag 128: correlation = 0.7787785
🎯 Economical lag 129: correlation = 0.73573214
🎯 Economical lag 130: correlation = 0.71718156
🎯 Economical lag 131: correlation = 0.7074901
🎯 Economical lag 132: correlation = 0.7071098
🎯 Economical lag 133: correlation = 0.71859604
🎯 Economical lag 134: correlation = 0.7100186
🎯 Economical lag 135: correlation = 0.6754826
🎯 Economical lag 136: correlation = 0.6153127
🎯 Economical lag 137: correlation = 0.56709045
🎯 Economical lag 138: correlation = 0.5473625
🎯 Economical lag 139: correlation = 0.5258411
🎯 Economical lag 140: correlation = 0.49656796
🎯 Economical lag 141: correlation = 0.44169453
🎯 Economical lag 142: correlation = 0.43710706
🎯 Economical lag 143: correlation = 0.45074496
🎯 Economical lag 144: correlation = 0.5869792
🎯 Economical lag 145: correlation = 0.5585523
🎯 Economical lag 146: correlation = 0.568178
🎯 Economical lag 147: correlation = 0.5361761
🎯 Economical lag 148: correlation = 0.50098914
🎯 Economical lag 149: correlation = 0.5037996
🎯 Economical lag 150: correlation = 0.47435263
🎯 Economical lag 151: correlation = 0.4036594
🎯 Economical lag 152: correlation = 0.3909118
🔍 Best correlation: 0.7787785 at period 128 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-10.416666 Hz (periods: 102-152)
📊 Moving averages updated - Period: 122.4488, Amplitude: 0.84406567, RunLength: 5
✅ Pitch detected: 8.333333 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 8.333333 Hz
🎵 Detected pitch: 8.333333 Hz (total samples: 441600)
� Calculated pressure: 4.665999 cm H2O
✅ Audio engine stopped
💾 Saved 10 recent sessions to file
✅ Session saved to history: 00:09, Quality: poor
🖥️ UI updated - Freq: 9.039548, Pressure: 5.4562535, Breaths: 1
🖥️ UI updated - Freq: 8.333333, Pressure: 4.665999, Breaths: 1