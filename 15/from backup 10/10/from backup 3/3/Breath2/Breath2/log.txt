✅ Loaded 6 recent sessions from file
PitchDetector initialized with configuration:
  Sample rate: 44100.0 Hz
  Downsample factor: 45
  Downsampled rate: 980.0 Hz
  Target frequency range: 7-40 Hz
  Initial target range: 7.0-40.0 Hz
  Smoothing filter size: 176
PressureCalculator initialized:
  Linear model: Pressure = -4.659 + 1.119 * Frequency
  Research equation: Pressure = -4.659 + 1.119 × Pitch
  r² = 0.886 across 9,993 data points
  Valid frequency range: 7.0-40.0 Hz
  Valid pressure range: 6.0-30.0 cm H2O
✅ Audio session configured successfully
✅ New therapy session started
🎬 startRecording called
🫁 Breath detection: Reset
🔧 Setting up audio engine...
Input format: <AVAudioFormat 0x11cfb7430:  1 ch,  48000 Hz, Float32>
PitchDetector initialized with configuration:
  Sample rate: 48000.0 Hz
  Downsample factor: 49
  Downsampled rate: 979.59186 Hz
  Target frequency range: 7-40 Hz
  Initial target range: 7.0-40.0 Hz
  Smoothing filter size: 192
🔄 New session started - all state variables reset
✅ Audio engine started successfully
App is being debugged, do not track this hang
Hang detected: 0.28s (debugger attached, not reporting)
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.0004258277, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 4800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 97 (target: 300)
🔧 Accumulating data... need 203 more samples
🎵 Detected pitch: 0.0 Hz (total samples: 4800)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.0005005899, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 9600
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 194 (target: 300)
🔧 Accumulating data... need 106 more samples
🎵 Detected pitch: 0.0 Hz (total samples: 9600)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00052615436, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 14400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 291 (target: 300)
🔧 Accumulating data... need 9 more samples
🎵 Detected pitch: 0.0 Hz (total samples: 14400)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00050226174, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 19200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 9.172069e-10, Max: 2.7365424e-08, Avg: 6.6525065e-09, Variance: 1.9403758e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 19200)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0007570414, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 24000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 6.215836e-10, Max: 4.1349953e-08, Avg: 6.56888e-09, Variance: 2.60393e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 24000)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.002212372, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 28800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 6.215836e-10, Max: 3.5319718e-07, Avg: 1.1371213e-08, Variance: 6.970658e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 28800)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0017897662, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 33600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 6.215836e-10, Max: 9.886027e-07, Avg: 1.9941766e-08, Variance: 5.670408e-15
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 33600)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00053489587, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 38400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.1131345e-09, Max: 9.886027e-07, Avg: 1.9423062e-08, Variance: 5.675273e-15
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 38400)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00062826974, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 43200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.1131345e-09, Max: 9.886027e-07, Avg: 1.6426752e-08, Variance: 5.4712454e-15
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 43200)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0021734862, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 48000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.1131345e-09, Max: 6.622796e-07, Avg: 1.3661544e-08, Variance: 3.518124e-15
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 48000)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00047482137, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 52800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 9.398313e-10, Max: 6.622796e-07, Avg: 1.3398441e-08, Variance: 3.5209317e-15
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 52800)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00045607216, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 57600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 9.398313e-10, Max: 6.622796e-07, Avg: 1.355231e-08, Variance: 3.5162595e-15
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 57600)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00069520244, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 62400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 9.398313e-10, Max: 3.714932e-08, Avg: 7.267869e-09, Variance: 3.029402e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 62400)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0004935706, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 67200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.0790433e-09, Max: 3.714932e-08, Avg: 8.434968e-09, Variance: 3.5809896e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 67200)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00066023355, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 72000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.4456842e-09, Max: 7.1012245e-08, Avg: 1.1041032e-08, Variance: 7.302289e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 72000)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0007577362, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 76800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.4926171e-09, Max: 7.1012245e-08, Avg: 1.2959287e-08, Variance: 9.602913e-17
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 76800)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00078166235, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 81600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.8858735e-09, Max: 7.1012245e-08, Avg: 1.5650985e-08, Variance: 1.0869055e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 81600)
� Calculated pressure: 0.0 cm H2O
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.000653745, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 86400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 2.758933e-09, Max: 5.773724e-08, Avg: 1.7572042e-08, Variance: 1.1739488e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 86400)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0008196744, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 91200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 3.2253817e-09, Max: 6.943829e-08, Avg: 2.1020236e-08, Variance: 1.6667426e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 91200)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0010210867, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 96000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 3.994448e-09, Max: 9.5653625e-08, Avg: 2.513971e-08, Variance: 2.8282986e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 96000)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0007597037, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 100800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 3.994448e-09, Max: 1.2712685e-07, Avg: 2.9711252e-08, Variance: 4.00606e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 100800)
� Calculated pressure: 0.0 cm H2O
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.0007454512, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 105600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 3.994448e-09, Max: 1.5072753e-07, Avg: 3.3547735e-08, Variance: 4.933189e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 105600)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0010097066, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 110400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 6.5501435e-09, Max: 1.5072753e-07, Avg: 4.1090143e-08, Variance: 7.1621797e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 110400)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00079770875, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 115200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 7.2413964e-09, Max: 1.5279494e-07, Avg: 4.6829058e-08, Variance: 8.2958423e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 115200)
� Calculated pressure: 0.0 cm H2O
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.0009207154, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 120000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 8.20612e-09, Max: 2.0674354e-07, Avg: 5.5486975e-08, Variance: 1.1453171e-15
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 120000)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0008640939, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 124800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 5.6665126e-09, Max: 2.0674354e-07, Avg: 4.9113307e-08, Variance: 1.1042887e-15
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 124800)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.00084208243, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 129600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.5275536e-09, Max: 2.0674354e-07, Avg: 3.7578307e-08, Variance: 1.182402e-15
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 129600)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.01356717, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 134400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.5275536e-09, Max: 4.512517e-05, Avg: 8.641339e-07, Variance: 2.0949667e-11
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 134400)
� Calculated pressure: 0.0 cm H2O
🫁 Breath detection: Starting new breath
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.026287552, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 139200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.5275536e-09, Max: 0.00017288746, Avg: 1.0969588e-05, Variance: 6.403973e-10
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=4.0170026e-08, norm1=1.8985598e-07, norm2=2.2821872e-07, result=0.1929809
🔍 Coarse search lag 24: correlation = 0.1929809
🔍 Lag 27: corr=2.5804352e-08, norm1=1.80348e-07, norm2=2.2821872e-07, result=0.12719254
🔍 Coarse search lag 27: correlation = 0.12719254
🔍 Lag 30: corr=1.9276296e-08, norm1=1.1502135e-07, norm2=2.2821871e-07, result=0.118975826
🔍 Coarse search lag 30: correlation = 0.118975826
🔍 Coarse search lag 48: correlation = 0.47186086
🔍 Coarse search lag 51: correlation = 0.52157974
🔍 Coarse search lag 54: correlation = 0.6107478
🔍 Coarse search lag 57: correlation = 0.54861265
🔍 Coarse search lag 60: correlation = 0.5876212
🔍 Coarse search lag 63: correlation = 0.55095106
🔍 Coarse search lag 66: correlation = 0.66307
🔍 Coarse search lag 69: correlation = 0.49954686
🔍 Coarse search lag 72: correlation = 0.45462987
🔍 Coarse search lag 75: correlation = 0.3604776
🔍 Coarse search lag 123: correlation = 0.37155566
🔍 Coarse search lag 126: correlation = 0.43799627
🔍 Coarse search lag 129: correlation = 0.45142806
🔍 Coarse search lag 132: correlation = 0.44669
🔍 Coarse search lag 135: correlation = 0.4908336
🔍 Coarse search lag 138: correlation = 0.3877497
🔍 Fine search around lag 66 in range [61, 71]
🔍 Fine search lag 61: correlation = 0.5768838
🔍 Fine search lag 62: correlation = 0.5726523
🔍 Fine search lag 63: correlation = 0.55095106
🔍 Fine search lag 64: correlation = 0.60067165
🔍 Fine search lag 65: correlation = 0.6383145
🔍 Fine search lag 66: correlation = 0.66307
🔍 Fine search lag 67: correlation = 0.5979326
🔍 Fine search lag 68: correlation = 0.5595871
🔍 Fine search lag 69: correlation = 0.49954686
🔍 Fine search lag 70: correlation = 0.4138081
🔍 Fine search lag 71: correlation = 0.43348154
🔍 Best correlation: 0.66307 at period 66 (threshold: 0.6) [strategy: full]
🎯 Target range updated: 11.131725-18.552876 Hz (periods: 52-88)
📊 Moving averages updated - Period: 66.0, Amplitude: 0.66307, RunLength: 1
✅ Pitch detected: 14.8423 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 14.8423 Hz
🎵 Detected pitch: 14.8423 Hz (total samples: 139200)
� Calculated pressure: 11.949533 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 14.8423, Pressure: 11.949533, Breaths: 0
🎤 Audio Level: 0.03030522, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 144000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.6961301e-09, Max: 0.00021696632, Avg: 3.0089077e-05, Variance: 2.069912e-09
🎯 Economical search: one wavelength ahead 132, derivative adj 0, predicted lag 132, window [119, 139]
🎯 Economical lag 119: correlation = 0.5180525
🎯 Economical lag 120: correlation = 0.4723775
🎯 Economical lag 121: correlation = 0.43520868
🎯 Economical lag 122: correlation = 0.43468395
🎯 Economical lag 123: correlation = 0.428773
🎯 Economical lag 124: correlation = 0.42499977
🎯 Economical lag 125: correlation = 0.43797296
🎯 Economical lag 126: correlation = 0.4357486
🎯 Economical lag 127: correlation = 0.4661383
🎯 Economical lag 128: correlation = 0.46088406
🎯 Economical lag 129: correlation = 0.4101304
🎯 Economical lag 130: correlation = 0.3568049
🎯 Economical lag 131: correlation = 0.3051789
🔄 Economical search failed (best: 0.5180525), falling back to full search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.4551465e-07, norm1=6.449976e-07, norm2=8.925793e-07, result=0.1917804
🔍 Coarse search lag 24: correlation = 0.1917804
🔍 Lag 27: corr=8.7472266e-08, norm1=5.7141403e-07, norm2=8.925793e-07, result=0.122481786
🔍 Coarse search lag 27: correlation = 0.122481786
🔍 Lag 30: corr=1.054008e-07, norm1=5.641557e-07, norm2=8.9257924e-07, result=0.14853235
🔍 Coarse search lag 30: correlation = 0.14853235
🔍 Coarse search lag 39: correlation = 0.41720763
🔍 Coarse search lag 42: correlation = 0.4473922
🔍 Coarse search lag 45: correlation = 0.54456794
🔍 Coarse search lag 48: correlation = 0.60003185
🔍 Coarse search lag 51: correlation = 0.6920967
🔍 Coarse search lag 54: correlation = 0.7247141
🔍 Coarse search lag 57: correlation = 0.68842226
🔍 Coarse search lag 60: correlation = 0.67857605
🔍 Coarse search lag 63: correlation = 0.5900436
🔍 Coarse search lag 66: correlation = 0.58186984
🔍 Coarse search lag 69: correlation = 0.46357214
🔍 Coarse search lag 72: correlation = 0.39408362
🔍 Coarse search lag 93: correlation = 0.3613913
🔍 Coarse search lag 96: correlation = 0.37386972
🔍 Coarse search lag 99: correlation = 0.4652521
🔍 Coarse search lag 102: correlation = 0.53407043
🔍 Coarse search lag 105: correlation = 0.65373564
🔍 Coarse search lag 108: correlation = 0.61734176
🔍 Coarse search lag 111: correlation = 0.67620665
🔍 Coarse search lag 114: correlation = 0.5957876
🔍 Coarse search lag 117: correlation = 0.60691905
🔍 Coarse search lag 120: correlation = 0.4723775
🔍 Coarse search lag 123: correlation = 0.428773
🔍 Coarse search lag 126: correlation = 0.4357486
🔍 Coarse search lag 129: correlation = 0.4101304
🔍 Fine search around lag 54 in range [49, 59]
🔍 Fine search lag 49: correlation = 0.67055666
🔍 Fine search lag 50: correlation = 0.6854009
🔍 Fine search lag 51: correlation = 0.6920967
🔍 Fine search lag 52: correlation = 0.6657448
🔍 Fine search lag 53: correlation = 0.6906621
🔍 Fine search lag 54: correlation = 0.7247141
🔍 Fine search lag 55: correlation = 0.7512715
🔍 Fine search lag 56: correlation = 0.7384875
🔍 Fine search lag 57: correlation = 0.68842226
🔍 Fine search lag 58: correlation = 0.65993834
🔍 Fine search lag 59: correlation = 0.6623118
🔍 Best correlation: 0.7512715 at period 55 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 13.35807-22.26345 Hz (periods: 44-73)
📊 Moving averages updated - Period: 60.5, Amplitude: 0.7071707, RunLength: 2
✅ Pitch detected: 17.81076 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 17.81076 Hz
🎵 Detected pitch: 17.81076 Hz (total samples: 144000)
� Calculated pressure: 15.27124 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 17.81076, Pressure: 15.27124, Breaths: 0
🎤 Audio Level: 0.029897507, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 148800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.1890063e-07, Max: 0.00027276063, Avg: 4.7992075e-05, Variance: 2.9804716e-09
🎯 Economical search: one wavelength ahead 110, derivative adj -5, predicted lag 105, window [94, 116]
🎯 Economical lag 94: correlation = 0.58937955
🎯 Economical lag 95: correlation = 0.59708947
🎯 Economical lag 96: correlation = 0.59333336
🎯 Economical lag 97: correlation = 0.6032411
🎯 Economical lag 98: correlation = 0.6510822
🎯 Economical lag 99: correlation = 0.68179065
🎯 Economical lag 100: correlation = 0.70039296
🎯 Economical lag 101: correlation = 0.6757006
🎯 Economical lag 102: correlation = 0.6534052
🎯 Economical lag 103: correlation = 0.6669066
🎯 Economical lag 104: correlation = 0.6945473
🎯 Economical lag 105: correlation = 0.72634715
🎯 Economical lag 106: correlation = 0.7053505
🎯 Economical lag 107: correlation = 0.69972974
🎯 Economical lag 108: correlation = 0.6708272
🎯 Economical lag 109: correlation = 0.6425887
🎯 Economical lag 110: correlation = 0.6395176
🎯 Economical lag 111: correlation = 0.61010474
🎯 Economical lag 112: correlation = 0.55859274
🎯 Economical lag 113: correlation = 0.5206514
🎯 Economical lag 114: correlation = 0.47782087
🎯 Economical lag 115: correlation = 0.46116713
🎯 Economical lag 116: correlation = 0.4642073
🔍 Best correlation: 0.72634715 at period 105 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-11.661807 Hz (periods: 84-139)
🔄 Run length reduced: 2 → 0 (leap: 6.862155 Hz, threshold: 3.23832 Hz)
🔄 Moving averages reset due to run length = 0
✅ Pitch detected: 9.329446 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.329446 Hz
🎵 Detected pitch: 9.329446 Hz (total samples: 148800)
� Calculated pressure: 5.7806497 cm H2O
🖥️ UI updated - Freq: 9.329446, Pressure: 5.7806497, Breaths: 0
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.037855826, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 153600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 2.1040965e-07, Max: 0.00035426728, Avg: 5.691635e-05, Variance: 4.563197e-09
🎯 Economical search: one wavelength ahead 210, derivative adj 0, predicted lag 210, window [189, 139]
⚠️ Invalid economical search range: 189 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=2.5480094e-07, norm1=2.0196321e-06, norm2=2.251684e-06, result=0.11948439
🔍 Coarse search lag 24: correlation = 0.11948439
🔍 Lag 27: corr=2.274377e-07, norm1=1.955391e-06, norm2=2.2288189e-06, result=0.108945236
🔍 Coarse search lag 27: correlation = 0.108945236
🔍 Lag 30: corr=3.4149053e-07, norm1=1.948761e-06, norm2=2.2150593e-06, result=0.164364
🔍 Coarse search lag 30: correlation = 0.164364
🔍 Coarse search lag 36: correlation = 0.35592732
🔍 Coarse search lag 39: correlation = 0.51133734
🔍 Coarse search lag 42: correlation = 0.60025823
🔍 Coarse search lag 45: correlation = 0.72093344
🔍 Coarse search lag 48: correlation = 0.73932445
🔍 Coarse search lag 51: correlation = 0.73697275
🔍 Coarse search lag 54: correlation = 0.662134
🔍 Coarse search lag 57: correlation = 0.56784534
🔍 Coarse search lag 60: correlation = 0.42893454
🔍 Coarse search lag 63: correlation = 0.3449747
🔍 Coarse search lag 84: correlation = 0.3430618
🔍 Coarse search lag 87: correlation = 0.48180664
🔍 Coarse search lag 90: correlation = 0.58580506
🔍 Coarse search lag 93: correlation = 0.6745005
🔍 Coarse search lag 96: correlation = 0.7435092
🔍 Coarse search lag 99: correlation = 0.7539799
🔍 Coarse search lag 102: correlation = 0.686879
🔍 Coarse search lag 105: correlation = 0.59348416
🔍 Coarse search lag 108: correlation = 0.49605256
🔍 Coarse search lag 111: correlation = 0.36497962
🔍 Coarse search lag 132: correlation = 0.33115816
🔍 Coarse search lag 135: correlation = 0.4196593
🔍 Coarse search lag 138: correlation = 0.5402014
🔍 Fine search around lag 99 in range [94, 104]
🔍 Fine search lag 94: correlation = 0.7078518
🔍 Fine search lag 95: correlation = 0.7234398
🔍 Fine search lag 96: correlation = 0.7435092
🔍 Fine search lag 97: correlation = 0.7720957
🔍 Fine search lag 98: correlation = 0.7909427
🔍 Fine search lag 99: correlation = 0.7539799
🔍 Fine search lag 100: correlation = 0.7160183
🔍 Fine search lag 101: correlation = 0.70920134
🔍 Fine search lag 102: correlation = 0.686879
🔍 Fine search lag 103: correlation = 0.6473591
🔍 Fine search lag 104: correlation = 0.6198604
🔍 Best correlation: 0.7909427 at period 98 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.4968767-12.494794 Hz (periods: 78-130)
📊 Moving averages updated - Period: 98.0, Amplitude: 0.7909427, RunLength: 1
✅ Pitch detected: 9.995835 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.995835 Hz
🎵 Detected pitch: 9.995835 Hz (total samples: 153600)
� Calculated pressure: 6.526339 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.995835, Pressure: 6.526339, Breaths: 0
🎤 Audio Level: 0.031494297, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 158400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 5.864166e-07, Max: 0.00035426728, Avg: 5.774966e-05, Variance: 4.890428e-09
🎯 Economical search: one wavelength ahead 196, derivative adj 0, predicted lag 196, window [177, 139]
⚠️ Invalid economical search range: 177 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=2.2237847e-07, norm1=2.225257e-06, norm2=2.4429257e-06, result=0.09537785
🔍 Coarse search lag 24: correlation = 0.09537785
🔍 Lag 27: corr=2.2554521e-07, norm1=2.1987053e-06, norm2=2.435786e-06, result=0.097460926
🔍 Coarse search lag 27: correlation = 0.097460926
🔍 Lag 30: corr=3.782633e-07, norm1=2.1388557e-06, norm2=2.4144013e-06, result=0.16645573
🔍 Coarse search lag 30: correlation = 0.16645573
🔍 Coarse search lag 36: correlation = 0.3131366
🔍 Coarse search lag 39: correlation = 0.49200964
🔍 Coarse search lag 42: correlation = 0.5765531
🔍 Coarse search lag 45: correlation = 0.7050473
🔍 Coarse search lag 48: correlation = 0.73575073
🔍 Coarse search lag 51: correlation = 0.74243575
🔍 Coarse search lag 54: correlation = 0.6242019
🔍 Coarse search lag 57: correlation = 0.53614974
🔍 Coarse search lag 60: correlation = 0.40286964
🔍 Coarse search lag 63: correlation = 0.31849372
🔍 Coarse search lag 87: correlation = 0.41224366
🔍 Coarse search lag 90: correlation = 0.5386378
🔍 Coarse search lag 93: correlation = 0.67272925
🔍 Coarse search lag 96: correlation = 0.74913925
🔍 Coarse search lag 99: correlation = 0.7313215
🔍 Coarse search lag 102: correlation = 0.7123435
🔍 Coarse search lag 105: correlation = 0.5496744
🔍 Coarse search lag 108: correlation = 0.45249233
🔍 Coarse search lag 111: correlation = 0.32621735
🔍 Coarse search lag 135: correlation = 0.38850814
🔍 Coarse search lag 138: correlation = 0.5262736
🔍 Fine search around lag 96 in range [91, 101]
🔍 Fine search lag 91: correlation = 0.5833291
🔍 Fine search lag 92: correlation = 0.6380183
🔍 Fine search lag 93: correlation = 0.67272925
🔍 Fine search lag 94: correlation = 0.6887991
🔍 Fine search lag 95: correlation = 0.72192615
🔍 Fine search lag 96: correlation = 0.74913925
🔍 Fine search lag 97: correlation = 0.7744923
🔍 Fine search lag 98: correlation = 0.77859634
🔍 Fine search lag 99: correlation = 0.7313215
🔍 Fine search lag 100: correlation = 0.7026101
🔍 Fine search lag 101: correlation = 0.7209234
🔍 Best correlation: 0.77859634 at period 98 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.4968767-12.494794 Hz (periods: 78-130)
📊 Moving averages updated - Period: 98.0, Amplitude: 0.78476954, RunLength: 2
✅ Pitch detected: 9.995835 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.995835 Hz
🎵 Detected pitch: 9.995835 Hz (total samples: 158400)
� Calculated pressure: 6.526339 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.995835, Pressure: 6.526339, Breaths: 0
🎤 Audio Level: 0.041008677, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 163200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 4.3957024e-07, Max: 0.00035426728, Avg: 6.0594106e-05, Variance: 5.787686e-09
🎯 Economical search: one wavelength ahead 196, derivative adj 0, predicted lag 196, window [177, 139]
⚠️ Invalid economical search range: 177 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.9724436e-07, norm1=2.6031187e-06, norm2=2.8321856e-06, result=0.0726435
🔍 Coarse search lag 24: correlation = 0.0726435
🔍 Lag 27: corr=1.9776343e-07, norm1=2.6020932e-06, norm2=2.8300728e-06, result=0.0728762
🔍 Coarse search lag 27: correlation = 0.0728762
🔍 Lag 30: corr=3.3402446e-07, norm1=2.6020666e-06, norm2=2.783834e-06, result=0.12410732
🔍 Coarse search lag 30: correlation = 0.12410732
🔍 Coarse search lag 39: correlation = 0.40490592
🔍 Coarse search lag 42: correlation = 0.5027414
🔍 Coarse search lag 45: correlation = 0.644281
🔍 Coarse search lag 48: correlation = 0.73045236
🔍 Coarse search lag 51: correlation = 0.7455398
🔍 Coarse search lag 54: correlation = 0.67730117
🔍 Coarse search lag 57: correlation = 0.608553
🔍 Coarse search lag 60: correlation = 0.48444054
🔍 Coarse search lag 63: correlation = 0.36618826
🔍 Coarse search lag 87: correlation = 0.34038928
🔍 Coarse search lag 90: correlation = 0.4366445
🔍 Coarse search lag 93: correlation = 0.5746448
🔍 Coarse search lag 96: correlation = 0.67428714
🔍 Coarse search lag 99: correlation = 0.7943519
🔍 Coarse search lag 102: correlation = 0.7350219
🔍 Coarse search lag 105: correlation = 0.6546159
🔍 Coarse search lag 108: correlation = 0.6049666
🔍 Coarse search lag 111: correlation = 0.47663972
🔍 Coarse search lag 114: correlation = 0.36657622
🔍 Coarse search lag 138: correlation = 0.3228062
🔍 Fine search around lag 99 in range [94, 104]
🔍 Fine search lag 94: correlation = 0.60543334
🔍 Fine search lag 95: correlation = 0.63648003
🔍 Fine search lag 96: correlation = 0.67428714
🔍 Fine search lag 97: correlation = 0.7310104
🔍 Fine search lag 98: correlation = 0.77190036
🔍 Fine search lag 99: correlation = 0.7943519
🔍 Fine search lag 100: correlation = 0.7802523
🔍 Fine search lag 101: correlation = 0.7597111
🔍 Fine search lag 102: correlation = 0.7350219
🔍 Fine search lag 103: correlation = 0.7239944
🔍 Fine search lag 104: correlation = 0.69814354
🔍 Best correlation: 0.7943519 at period 99 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.42115-12.368584 Hz (periods: 79-132)
📊 Moving averages updated - Period: 98.33333, Amplitude: 0.7879636, RunLength: 3
✅ Pitch detected: 9.894867 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.894867 Hz
🎵 Detected pitch: 9.894867 Hz (total samples: 163200)
� Calculated pressure: 6.4133554 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.894867, Pressure: 6.4133554, Breaths: 0
🎤 Audio Level: 0.030447092, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 168000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 3.7878732e-07, Max: 0.00034008836, Avg: 5.965773e-05, Variance: 4.9940505e-09
🎯 Economical search: one wavelength ahead 198, derivative adj 0, predicted lag 198, window [179, 139]
⚠️ Invalid economical search range: 179 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=2.467735e-07, norm1=2.273415e-06, norm2=2.562127e-06, result=0.10224895
🔍 Coarse search lag 24: correlation = 0.10224895
🔍 Lag 27: corr=2.6810133e-07, norm1=2.2733982e-06, norm2=2.5616266e-06, result=0.111097254
🔍 Coarse search lag 27: correlation = 0.111097254
🔍 Lag 30: corr=3.818988e-07, norm1=2.273369e-06, norm2=2.556524e-06, result=0.15841211
🔍 Coarse search lag 30: correlation = 0.15841211
🔍 Coarse search lag 36: correlation = 0.30359113
🔍 Coarse search lag 39: correlation = 0.45903385
🔍 Coarse search lag 42: correlation = 0.54837143
🔍 Coarse search lag 45: correlation = 0.63932073
🔍 Coarse search lag 48: correlation = 0.7194968
🔍 Coarse search lag 51: correlation = 0.756198
🔍 Coarse search lag 54: correlation = 0.7152062
🔍 Coarse search lag 57: correlation = 0.6505238
🔍 Coarse search lag 60: correlation = 0.5494779
🔍 Coarse search lag 63: correlation = 0.4203127
🔍 Coarse search lag 66: correlation = 0.31977636
🔍 Coarse search lag 87: correlation = 0.3456981
🔍 Coarse search lag 90: correlation = 0.4275037
🔍 Coarse search lag 93: correlation = 0.5421434
🔍 Coarse search lag 96: correlation = 0.6445017
🔍 Coarse search lag 99: correlation = 0.77569807
🔍 Coarse search lag 102: correlation = 0.76097935
🔍 Coarse search lag 105: correlation = 0.70181775
🔍 Coarse search lag 108: correlation = 0.68537515
🔍 Coarse search lag 111: correlation = 0.5463776
🔍 Coarse search lag 114: correlation = 0.41297966
🔍 Fine search around lag 99 in range [94, 104]
🔍 Fine search lag 94: correlation = 0.58019876
🔍 Fine search lag 95: correlation = 0.60700184
🔍 Fine search lag 96: correlation = 0.6445017
🔍 Fine search lag 97: correlation = 0.71803266
🔍 Fine search lag 98: correlation = 0.7540957
🔍 Fine search lag 99: correlation = 0.77569807
🔍 Fine search lag 100: correlation = 0.77790433
🔍 Fine search lag 101: correlation = 0.77237165
🔍 Fine search lag 102: correlation = 0.76097935
🔍 Fine search lag 103: correlation = 0.740224
🔍 Fine search lag 104: correlation = 0.7124785
🔍 Best correlation: 0.77790433 at period 100 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.346939-12.244898 Hz (periods: 80-133)
📊 Moving averages updated - Period: 98.75, Amplitude: 0.7854488, RunLength: 4
✅ Pitch detected: 9.795918 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.795918 Hz
🎵 Detected pitch: 9.795918 Hz (total samples: 168000)
� Calculated pressure: 6.302633 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.795918, Pressure: 6.302633, Breaths: 0
🎤 Audio Level: 0.03456045, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 172800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 3.7878732e-07, Max: 0.0004188691, Avg: 6.148898e-05, Variance: 5.7790306e-09
🎯 Economical search: one wavelength ahead 200, derivative adj 0, predicted lag 200, window [180, 139]
⚠️ Invalid economical search range: 180 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=2.1116124e-07, norm1=2.1915735e-06, norm2=2.8300512e-06, result=0.0847889
🔍 Coarse search lag 24: correlation = 0.0847889
🔍 Lag 27: corr=2.4987517e-07, norm1=2.1915657e-06, norm2=2.8300415e-06, result=0.1003343
🔍 Coarse search lag 27: correlation = 0.1003343
🔍 Lag 30: corr=3.255323e-07, norm1=2.191558e-06, norm2=2.829744e-06, result=0.1307206
🔍 Coarse search lag 30: correlation = 0.1307206
🔍 Coarse search lag 36: correlation = 0.31315342
🔍 Coarse search lag 39: correlation = 0.45369697
🔍 Coarse search lag 42: correlation = 0.543877
🔍 Coarse search lag 45: correlation = 0.7041195
🔍 Coarse search lag 48: correlation = 0.6957279
🔍 Coarse search lag 51: correlation = 0.74357146
🔍 Coarse search lag 54: correlation = 0.7064666
🔍 Coarse search lag 57: correlation = 0.5907696
🔍 Coarse search lag 60: correlation = 0.50057405
🔍 Coarse search lag 63: correlation = 0.3842849
🔍 Coarse search lag 87: correlation = 0.347389
🔍 Coarse search lag 90: correlation = 0.48657182
🔍 Coarse search lag 93: correlation = 0.56719595
🔍 Coarse search lag 96: correlation = 0.6970514
🔍 Coarse search lag 99: correlation = 0.7350168
🔍 Coarse search lag 102: correlation = 0.7918041
🔍 Coarse search lag 105: correlation = 0.69207174
🔍 Coarse search lag 108: correlation = 0.6722153
🔍 Coarse search lag 111: correlation = 0.48370752
🔍 Coarse search lag 114: correlation = 0.37649694
🔍 Coarse search lag 135: correlation = 0.3184068
🔍 Coarse search lag 138: correlation = 0.35264134
🔍 Fine search around lag 102 in range [97, 107]
🔍 Fine search lag 97: correlation = 0.74764407
🔍 Fine search lag 98: correlation = 0.7607796
🔍 Fine search lag 99: correlation = 0.7350168
🔍 Fine search lag 100: correlation = 0.7453396
🔍 Fine search lag 101: correlation = 0.77097654
🔍 Fine search lag 102: correlation = 0.7918041
🔍 Fine search lag 103: correlation = 0.7559806
🔍 Fine search lag 104: correlation = 0.70365137
🔍 Fine search lag 105: correlation = 0.69207174
🔍 Fine search lag 106: correlation = 0.6989607
🔍 Fine search lag 107: correlation = 0.710757
🔍 Best correlation: 0.7918041 at period 102 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.2028813-12.004803 Hz (periods: 81-136)
📊 Moving averages updated - Period: 99.4, Amplitude: 0.7867198, RunLength: 5
✅ Pitch detected: 9.603842 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.603842 Hz
🎵 Detected pitch: 9.603842 Hz (total samples: 172800)
� Calculated pressure: 6.0876985 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.603842, Pressure: 6.0876985, Breaths: 0
🎤 Audio Level: 0.032203957, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 177600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 2.7318316e-07, Max: 0.0004188691, Avg: 5.5885022e-05, Variance: 5.119986e-09
🎯 Economical search: one wavelength ahead 204, derivative adj 0, predicted lag 204, window [184, 139]
⚠️ Invalid economical search range: 184 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.8505536e-07, norm1=2.452528e-06, norm2=2.4012925e-06, result=0.07625567
🔍 Coarse search lag 24: correlation = 0.07625567
🔍 Lag 27: corr=2.1795753e-07, norm1=2.452519e-06, norm2=2.401286e-06, result=0.08981393
🔍 Coarse search lag 27: correlation = 0.08981393
🔍 Lag 30: corr=2.7111324e-07, norm1=2.4524127e-06, norm2=2.4012825e-06, result=0.11172036
🔍 Coarse search lag 30: correlation = 0.11172036
🔍 Coarse search lag 39: correlation = 0.3672038
🔍 Coarse search lag 42: correlation = 0.4624154
🔍 Coarse search lag 45: correlation = 0.6434022
🔍 Coarse search lag 48: correlation = 0.6625368
🔍 Coarse search lag 51: correlation = 0.7446981
🔍 Coarse search lag 54: correlation = 0.72426045
🔍 Coarse search lag 57: correlation = 0.64674586
🔍 Coarse search lag 60: correlation = 0.6006647
🔍 Coarse search lag 63: correlation = 0.42564273
🔍 Coarse search lag 90: correlation = 0.36491594
🔍 Coarse search lag 93: correlation = 0.41793647
🔍 Coarse search lag 96: correlation = 0.5570123
🔍 Coarse search lag 99: correlation = 0.6325935
🔍 Coarse search lag 102: correlation = 0.6931156
🔍 Coarse search lag 105: correlation = 0.7480463
🔍 Coarse search lag 108: correlation = 0.68397355
🔍 Coarse search lag 111: correlation = 0.5068048
🔍 Coarse search lag 114: correlation = 0.39736173
🔍 Fine search around lag 105 in range [100, 110]
🔍 Fine search lag 100: correlation = 0.6466933
🔍 Fine search lag 101: correlation = 0.6791429
🔍 Fine search lag 102: correlation = 0.6931156
🔍 Fine search lag 103: correlation = 0.6787419
🔍 Fine search lag 104: correlation = 0.7186653
🔍 Fine search lag 105: correlation = 0.7480463
🔍 Fine search lag 106: correlation = 0.7009123
🔍 Fine search lag 107: correlation = 0.69858575
🔍 Fine search lag 108: correlation = 0.68397355
🔍 Fine search lag 109: correlation = 0.6222251
🔍 Fine search lag 110: correlation = 0.525574
🔍 Best correlation: 0.7480463 at period 105 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.0-11.661807 Hz (periods: 84-139)
📊 Moving averages updated - Period: 100.520004, Amplitude: 0.77898514, RunLength: 5
✅ Pitch detected: 9.329446 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.329446 Hz
🎵 Detected pitch: 9.329446 Hz (total samples: 177600)
� Calculated pressure: 5.7806497 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.329446, Pressure: 5.7806497, Breaths: 0
🎤 Audio Level: 0.035445176, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 182400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 2.7318316e-07, Max: 0.0004188691, Avg: 5.4270487e-05, Variance: 5.3685385e-09
🎯 Economical search: one wavelength ahead 210, derivative adj 0, predicted lag 210, window [189, 139]
⚠️ Invalid economical search range: 189 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.5065714e-07, norm1=2.4907256e-06, norm2=2.2790794e-06, result=0.06323348
🔍 Coarse search lag 24: correlation = 0.06323348
🔍 Lag 27: corr=1.383123e-07, norm1=2.486317e-06, norm2=2.279016e-06, result=0.05810438
🔍 Coarse search lag 27: correlation = 0.05810438
🔍 Lag 30: corr=1.9753018e-07, norm1=2.4775698e-06, norm2=2.278976e-06, result=0.08312863
🔍 Coarse search lag 30: correlation = 0.08312863
🔍 Coarse search lag 39: correlation = 0.3055265
🔍 Coarse search lag 42: correlation = 0.41572174
🔍 Coarse search lag 45: correlation = 0.5856337
🔍 Coarse search lag 48: correlation = 0.610405
🔍 Coarse search lag 51: correlation = 0.70597076
🔍 Coarse search lag 54: correlation = 0.69684714
🔍 Coarse search lag 57: correlation = 0.65139073
🔍 Coarse search lag 60: correlation = 0.5892272
🔍 Coarse search lag 63: correlation = 0.4055506
🔍 Coarse search lag 93: correlation = 0.32379752
🔍 Coarse search lag 96: correlation = 0.5020591
🔍 Coarse search lag 99: correlation = 0.5616656
🔍 Coarse search lag 102: correlation = 0.6823977
🔍 Coarse search lag 105: correlation = 0.73837554
🔍 Coarse search lag 108: correlation = 0.75480974
🔍 Coarse search lag 111: correlation = 0.6272647
🔍 Coarse search lag 114: correlation = 0.5128951
🔍 Coarse search lag 117: correlation = 0.36306944
🔍 Fine search around lag 108 in range [103, 113]
🔍 Fine search lag 103: correlation = 0.6709178
🔍 Fine search lag 104: correlation = 0.71266323
🔍 Fine search lag 105: correlation = 0.73837554
🔍 Fine search lag 106: correlation = 0.75108325
🔍 Fine search lag 107: correlation = 0.75640166
🔍 Fine search lag 108: correlation = 0.75480974
🔍 Fine search lag 109: correlation = 0.71754307
🔍 Fine search lag 110: correlation = 0.65413034
🔍 Fine search lag 111: correlation = 0.6272647
🔍 Fine search lag 112: correlation = 0.62064904
🔍 Fine search lag 113: correlation = 0.5672028
🔍 Best correlation: 0.75640166 at period 107 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.0-11.44383 Hz (periods: 85-139)
📊 Moving averages updated - Period: 101.81601, Amplitude: 0.7744685, RunLength: 5
✅ Pitch detected: 9.155064 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.155064 Hz
🎵 Detected pitch: 9.155064 Hz (total samples: 182400)
� Calculated pressure: 5.5855155 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.155064, Pressure: 5.5855155, Breaths: 0
🎤 Audio Level: 0.039582137, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 187200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 2.7318316e-07, Max: 0.00050710735, Avg: 6.142074e-05, Variance: 7.374773e-09
🎯 Economical search: one wavelength ahead 214, derivative adj 1, predicted lag 215, window [194, 139]
⚠️ Invalid economical search range: 194 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=2.1246925e-07, norm1=3.3276374e-06, norm2=2.5640418e-06, result=0.0727387
🔍 Coarse search lag 24: correlation = 0.0727387
🔍 Lag 27: corr=1.7373856e-07, norm1=3.3010822e-06, norm2=2.5640134e-06, result=0.059718363
🔍 Coarse search lag 27: correlation = 0.059718363
🔍 Lag 30: corr=2.4447772e-07, norm1=3.2732248e-06, norm2=2.5639486e-06, result=0.08439111
🔍 Coarse search lag 30: correlation = 0.08439111
🔍 Coarse search lag 39: correlation = 0.30541205
🔍 Coarse search lag 42: correlation = 0.4427818
🔍 Coarse search lag 45: correlation = 0.57193065
🔍 Coarse search lag 48: correlation = 0.56540334
🔍 Coarse search lag 51: correlation = 0.6436889
🔍 Coarse search lag 54: correlation = 0.6829369
🔍 Coarse search lag 57: correlation = 0.6259313
🔍 Coarse search lag 60: correlation = 0.5000112
🔍 Coarse search lag 63: correlation = 0.34904972
🔍 Coarse search lag 93: correlation = 0.43378013
🔍 Coarse search lag 96: correlation = 0.6095516
🔍 Coarse search lag 99: correlation = 0.5929251
🔍 Coarse search lag 102: correlation = 0.5942913
🔍 Coarse search lag 105: correlation = 0.60970336
🔍 Coarse search lag 108: correlation = 0.6060306
🔍 Coarse search lag 111: correlation = 0.47144994
🔍 Coarse search lag 114: correlation = 0.36550498
🔍 Fine search around lag 54 in range [49, 59]
🔍 Fine search lag 49: correlation = 0.5816027
🔍 Fine search lag 50: correlation = 0.5976168
🔍 Fine search lag 51: correlation = 0.6436889
🔍 Fine search lag 52: correlation = 0.6907183
🔍 Fine search lag 53: correlation = 0.6976579
🔍 Fine search lag 54: correlation = 0.6829369
🔍 Fine search lag 55: correlation = 0.67080665
🔍 Fine search lag 56: correlation = 0.65361637
🔍 Fine search lag 57: correlation = 0.6259313
🔍 Fine search lag 58: correlation = 0.56357896
🔍 Fine search lag 59: correlation = 0.5130339
🔍 Best correlation: 0.6976579 at period 53 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 13.862148-23.10358 Hz (periods: 42-70)
🔄 Run length reduced: 5 → 1 (leap: 8.861668 Hz, threshold: 1.9242394 Hz)
📊 Moving averages updated - Period: 77.408005, Amplitude: 0.7360632, RunLength: 2
✅ Pitch detected: 18.482864 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 18.482864 Hz
🎵 Detected pitch: 18.482864 Hz (total samples: 187200)
� Calculated pressure: 16.023325 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 18.482864, Pressure: 16.023325, Breaths: 0
🎤 Audio Level: 0.02858741, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 192000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 8.177455e-07, Max: 0.00050710735, Avg: 5.5222412e-05, Variance: 5.3952607e-09
🎯 Economical search: one wavelength ahead 106, derivative adj -23, predicted lag 83, window [73, 93]
🎯 Economical lag 86: correlation = 0.313294
🎯 Economical lag 87: correlation = 0.36144787
🎯 Economical lag 88: correlation = 0.39521086
🎯 Economical lag 89: correlation = 0.42858183
🎯 Economical lag 90: correlation = 0.44811141
🎯 Economical lag 91: correlation = 0.49447364
🎯 Economical lag 92: correlation = 0.52626485
🎯 Economical lag 93: correlation = 0.57615244
🔄 Economical search failed (best: 0.57615244), falling back to full search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=2.541278e-07, norm1=2.5187278e-06, norm2=2.3961336e-06, result=0.103444174
🔍 Coarse search lag 24: correlation = 0.103444174
🔍 Lag 27: corr=2.2044969e-07, norm1=2.5067084e-06, norm2=2.3879472e-06, result=0.09010424
🔍 Coarse search lag 27: correlation = 0.09010424
🔍 Lag 30: corr=3.0729387e-07, norm1=2.4637877e-06, norm2=2.3846258e-06, result=0.12677749
🔍 Coarse search lag 30: correlation = 0.12677749
🔍 Coarse search lag 39: correlation = 0.3791519
🔍 Coarse search lag 42: correlation = 0.51206714
🔍 Coarse search lag 45: correlation = 0.6179083
🔍 Coarse search lag 48: correlation = 0.5767567
🔍 Coarse search lag 51: correlation = 0.58189744
🔍 Coarse search lag 54: correlation = 0.6226314
🔍 Coarse search lag 57: correlation = 0.52497745
🔍 Coarse search lag 60: correlation = 0.3673369
🔍 Coarse search lag 87: correlation = 0.36144787
🔍 Coarse search lag 90: correlation = 0.44811141
🔍 Coarse search lag 93: correlation = 0.57615244
🔍 Coarse search lag 96: correlation = 0.7233405
🔍 Coarse search lag 99: correlation = 0.662501
🔍 Coarse search lag 102: correlation = 0.57797545
🔍 Coarse search lag 105: correlation = 0.54908204
🔍 Coarse search lag 108: correlation = 0.42157346
🔍 Coarse search lag 111: correlation = 0.33407766
🔍 Coarse search lag 138: correlation = 0.3498208
🔍 Fine search around lag 96 in range [91, 101]
🔍 Fine search lag 91: correlation = 0.49447364
🔍 Fine search lag 92: correlation = 0.52626485
🔍 Fine search lag 93: correlation = 0.57615244
🔍 Fine search lag 94: correlation = 0.6281615
🔍 Fine search lag 95: correlation = 0.67364967
🔍 Fine search lag 96: correlation = 0.7233405
🔍 Fine search lag 97: correlation = 0.7318456
🔍 Fine search lag 98: correlation = 0.68695974
🔍 Fine search lag 99: correlation = 0.662501
🔍 Fine search lag 100: correlation = 0.6542757
🔍 Fine search lag 101: correlation = 0.6093565
🔍 Best correlation: 0.7318456 at period 97 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.5741644-12.623607 Hz (periods: 77-129)
🔄 Run length reduced: 2 → 1 (leap: 2.5560312 Hz, threshold: 2.5309834 Hz)
📊 Moving averages updated - Period: 87.204, Amplitude: 0.7339544, RunLength: 2
✅ Pitch detected: 10.098886 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 10.098886 Hz
🎵 Detected pitch: 10.098886 Hz (total samples: 192000)
� Calculated pressure: 6.6416526 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 10.098886, Pressure: 6.6416526, Breaths: 0
🎤 Audio Level: 0.03652963, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 196800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 8.306157e-07, Max: 0.00050710735, Avg: 5.6236484e-05, Variance: 5.5561222e-09
🎯 Economical search: one wavelength ahead 194, derivative adj -2, predicted lag 192, window [173, 139]
⚠️ Invalid economical search range: 173 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=2.4532088e-07, norm1=2.4956544e-06, norm2=2.3749458e-06, result=0.10076633
🔍 Coarse search lag 24: correlation = 0.10076633
🔍 Lag 27: corr=2.1028893e-07, norm1=2.2730887e-06, norm2=2.3021917e-06, result=0.09192581
🔍 Coarse search lag 27: correlation = 0.09192581
🔍 Lag 30: corr=2.9789717e-07, norm1=2.2459994e-06, norm2=2.244225e-06, result=0.132687
🔍 Coarse search lag 30: correlation = 0.132687
🔍 Coarse search lag 39: correlation = 0.3566189
🔍 Coarse search lag 42: correlation = 0.49295032
🔍 Coarse search lag 45: correlation = 0.6563507
🔍 Coarse search lag 48: correlation = 0.62263423
🔍 Coarse search lag 51: correlation = 0.59410983
🔍 Coarse search lag 54: correlation = 0.6437844
🔍 Coarse search lag 57: correlation = 0.5631712
🔍 Coarse search lag 60: correlation = 0.39703324
🔍 Coarse search lag 63: correlation = 0.31717178
🔍 Coarse search lag 87: correlation = 0.35663384
🔍 Coarse search lag 90: correlation = 0.49025652
🔍 Coarse search lag 93: correlation = 0.57632166
🔍 Coarse search lag 96: correlation = 0.6607335
🔍 Coarse search lag 99: correlation = 0.68952566
🔍 Coarse search lag 102: correlation = 0.65908843
🔍 Coarse search lag 105: correlation = 0.6230321
🔍 Coarse search lag 108: correlation = 0.491715
🔍 Coarse search lag 111: correlation = 0.42214704
🔍 Coarse search lag 114: correlation = 0.35944635
🔍 Coarse search lag 138: correlation = 0.3554925
🔍 Fine search around lag 99 in range [94, 104]
🔍 Fine search lag 94: correlation = 0.56323546
🔍 Fine search lag 95: correlation = 0.5701908
🔍 Fine search lag 96: correlation = 0.6607335
🔍 Fine search lag 97: correlation = 0.67999196
🔍 Fine search lag 98: correlation = 0.67938703
🔍 Fine search lag 99: correlation = 0.68952566
🔍 Fine search lag 100: correlation = 0.72099245
🔍 Fine search lag 101: correlation = 0.6751233
🔍 Fine search lag 102: correlation = 0.65908843
🔍 Fine search lag 103: correlation = 0.63967866
🔍 Fine search lag 104: correlation = 0.61077064
🔍 Best correlation: 0.72099245 at period 100 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.346939-12.244898 Hz (periods: 80-133)
📊 Moving averages updated - Period: 91.46933, Amplitude: 0.7296338, RunLength: 3
✅ Pitch detected: 9.795918 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.795918 Hz
🎵 Detected pitch: 9.795918 Hz (total samples: 196800)
� Calculated pressure: 6.302633 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.795918, Pressure: 6.302633, Breaths: 0
🎤 Audio Level: 0.03412369, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 201600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 6.484996e-07, Max: 0.00038141766, Avg: 4.9677306e-05, Variance: 3.860545e-09
🎯 Economical search: one wavelength ahead 200, derivative adj 1, predicted lag 201, window [181, 139]
⚠️ Invalid economical search range: 181 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.7660668e-07, norm1=1.4062302e-06, norm2=1.7935632e-06, result=0.111204036
🔍 Coarse search lag 24: correlation = 0.111204036
🔍 Lag 27: corr=1.599407e-07, norm1=1.4015806e-06, norm2=1.7822538e-06, result=0.10119642
🔍 Coarse search lag 27: correlation = 0.10119642
🔍 Lag 30: corr=2.0943533e-07, norm1=1.4002771e-06, norm2=1.7596338e-06, result=0.13342333
🔍 Coarse search lag 30: correlation = 0.13342333
🔍 Coarse search lag 39: correlation = 0.3515014
🔍 Coarse search lag 42: correlation = 0.48449093
🔍 Coarse search lag 45: correlation = 0.65070206
🔍 Coarse search lag 48: correlation = 0.70268625
🔍 Coarse search lag 51: correlation = 0.67143697
🔍 Coarse search lag 54: correlation = 0.7172287
🔍 Coarse search lag 57: correlation = 0.66749424
🔍 Coarse search lag 60: correlation = 0.53044015
🔍 Coarse search lag 63: correlation = 0.4463693
🔍 Coarse search lag 66: correlation = 0.33712912
🔍 Coarse search lag 90: correlation = 0.3410782
🔍 Coarse search lag 93: correlation = 0.42674983
🔍 Coarse search lag 96: correlation = 0.5273999
🔍 Coarse search lag 99: correlation = 0.67535084
🔍 Coarse search lag 102: correlation = 0.78324896
🔍 Coarse search lag 105: correlation = 0.67327076
🔍 Coarse search lag 108: correlation = 0.5985933
🔍 Coarse search lag 111: correlation = 0.6150359
🔍 Coarse search lag 114: correlation = 0.5272423
🔍 Coarse search lag 117: correlation = 0.35996506
🔍 Fine search around lag 102 in range [97, 107]
🔍 Fine search lag 97: correlation = 0.5247168
🔍 Fine search lag 98: correlation = 0.57015586
🔍 Fine search lag 99: correlation = 0.67535084
🔍 Fine search lag 100: correlation = 0.77735466
🔍 Fine search lag 101: correlation = 0.79932433
🔍 Fine search lag 102: correlation = 0.78324896
🔍 Fine search lag 103: correlation = 0.74093825
🔍 Fine search lag 104: correlation = 0.69562614
🔍 Fine search lag 105: correlation = 0.67327076
🔍 Fine search lag 106: correlation = 0.6442875
🔍 Fine search lag 107: correlation = 0.6267657
🔍 Best correlation: 0.79932433 at period 101 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.2741966-12.123661 Hz (periods: 80-134)
📊 Moving averages updated - Period: 93.852, Amplitude: 0.7470564, RunLength: 4
✅ Pitch detected: 9.698929 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.698929 Hz
🎵 Detected pitch: 9.698929 Hz (total samples: 201600)
� Calculated pressure: 6.194101 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.698929, Pressure: 6.194101, Breaths: 0
🎤 Audio Level: 0.03085218, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 206400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 6.484996e-07, Max: 0.00038141766, Avg: 4.963617e-05, Variance: 3.9945744e-09
🎯 Economical search: one wavelength ahead 202, derivative adj 2, predicted lag 204, window [184, 139]
⚠️ Invalid economical search range: 184 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.5240731e-07, norm1=1.759096e-06, norm2=1.8599306e-06, result=0.084258296
🔍 Coarse search lag 24: correlation = 0.084258296
🔍 Lag 27: corr=1.3258929e-07, norm1=1.7589816e-06, norm2=1.8189464e-06, result=0.074125536
🔍 Coarse search lag 27: correlation = 0.074125536
🔍 Lag 30: corr=1.699533e-07, norm1=1.7589755e-06, norm2=1.7661582e-06, result=0.09642395
🔍 Coarse search lag 30: correlation = 0.09642395
🔍 Coarse search lag 39: correlation = 0.31540987
🔍 Coarse search lag 42: correlation = 0.4126346
🔍 Coarse search lag 45: correlation = 0.5815406
🔍 Coarse search lag 48: correlation = 0.6446897
🔍 Coarse search lag 51: correlation = 0.62578785
🔍 Coarse search lag 54: correlation = 0.7022503
🔍 Coarse search lag 57: correlation = 0.66716266
🔍 Coarse search lag 60: correlation = 0.5292696
🔍 Coarse search lag 63: correlation = 0.4751225
🔍 Coarse search lag 66: correlation = 0.33647612
🔍 Coarse search lag 93: correlation = 0.32047302
🔍 Coarse search lag 96: correlation = 0.4160216
🔍 Coarse search lag 99: correlation = 0.5709639
🔍 Coarse search lag 102: correlation = 0.7460948
🔍 Coarse search lag 105: correlation = 0.62576205
🔍 Coarse search lag 108: correlation = 0.57804936
🔍 Coarse search lag 111: correlation = 0.67819786
🔍 Coarse search lag 114: correlation = 0.5784491
🔍 Coarse search lag 117: correlation = 0.44845936
🔍 Coarse search lag 120: correlation = 0.3273323
🔍 Fine search around lag 102 in range [97, 107]
🔍 Fine search lag 97: correlation = 0.447871
🔍 Fine search lag 98: correlation = 0.48323232
🔍 Fine search lag 99: correlation = 0.5709639
🔍 Fine search lag 100: correlation = 0.6616154
🔍 Fine search lag 101: correlation = 0.7185816
🔍 Fine search lag 102: correlation = 0.7460948
🔍 Fine search lag 103: correlation = 0.72344553
🔍 Fine search lag 104: correlation = 0.70153356
🔍 Fine search lag 105: correlation = 0.62576205
🔍 Fine search lag 106: correlation = 0.5870773
🔍 Fine search lag 107: correlation = 0.5724786
🔍 Best correlation: 0.7460948 at period 102 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.2028813-12.004803 Hz (periods: 81-136)
📊 Moving averages updated - Period: 95.4816, Amplitude: 0.74686414, RunLength: 5
✅ Pitch detected: 9.603842 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.603842 Hz
🎵 Detected pitch: 9.603842 Hz (total samples: 206400)
� Calculated pressure: 6.0876985 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🫁 Breath detection: Breath now active (duration: 1.5s)
🖥️ UI updated - Freq: 9.603842, Pressure: 6.0876985, Breaths: 0
🎤 Audio Level: 0.03359692, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 211200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 6.484996e-07, Max: 0.00038141766, Avg: 4.7319176e-05, Variance: 3.826484e-09
🎯 Economical search: one wavelength ahead 204, derivative adj 2, predicted lag 206, window [186, 139]
⚠️ Invalid economical search range: 186 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.6557351e-07, norm1=1.6777586e-06, norm2=1.8080204e-06, result=0.09506583
🔍 Coarse search lag 24: correlation = 0.09506583
🔍 Lag 27: corr=1.514773e-07, norm1=1.6777333e-06, norm2=1.77415e-06, result=0.08779927
🔍 Coarse search lag 27: correlation = 0.08779927
🔍 Lag 30: corr=1.7562463e-07, norm1=1.6777087e-06, norm2=1.7466293e-06, result=0.10259514
🔍 Coarse search lag 30: correlation = 0.10259514
🔍 Coarse search lag 39: correlation = 0.30469918
🔍 Coarse search lag 42: correlation = 0.41094756
🔍 Coarse search lag 45: correlation = 0.53721553
🔍 Coarse search lag 48: correlation = 0.62835944
🔍 Coarse search lag 51: correlation = 0.63934946
🔍 Coarse search lag 54: correlation = 0.68304765
🔍 Coarse search lag 57: correlation = 0.69997615
🔍 Coarse search lag 60: correlation = 0.56478304
🔍 Coarse search lag 63: correlation = 0.47116297
🔍 Coarse search lag 66: correlation = 0.35384232
🔍 Coarse search lag 93: correlation = 0.3832116
🔍 Coarse search lag 96: correlation = 0.53432304
🔍 Coarse search lag 99: correlation = 0.5999434
🔍 Coarse search lag 102: correlation = 0.743591
🔍 Coarse search lag 105: correlation = 0.7143917
🔍 Coarse search lag 108: correlation = 0.64701587
🔍 Coarse search lag 111: correlation = 0.65735817
🔍 Coarse search lag 114: correlation = 0.55427766
🔍 Coarse search lag 117: correlation = 0.43634674
🔍 Coarse search lag 120: correlation = 0.3036446
🔍 Fine search around lag 102 in range [97, 107]
🔍 Fine search lag 97: correlation = 0.5815157
🔍 Fine search lag 98: correlation = 0.5829657
🔍 Fine search lag 99: correlation = 0.5999434
🔍 Fine search lag 100: correlation = 0.6216942
🔍 Fine search lag 101: correlation = 0.6797893
🔍 Fine search lag 102: correlation = 0.743591
🔍 Fine search lag 103: correlation = 0.74884707
🔍 Fine search lag 104: correlation = 0.7588229
🔍 Fine search lag 105: correlation = 0.7143917
🔍 Fine search lag 106: correlation = 0.6788325
🔍 Fine search lag 107: correlation = 0.6338254
🔍 Best correlation: 0.7588229 at period 104 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.0643644-11.77394 Hz (periods: 83-138)
📊 Moving averages updated - Period: 97.18527, Amplitude: 0.7492559, RunLength: 5
✅ Pitch detected: 9.419152 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.419152 Hz
🎵 Detected pitch: 9.419152 Hz (total samples: 211200)
� Calculated pressure: 5.8810315 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.419152, Pressure: 5.8810315, Breaths: 0
🎤 Audio Level: 0.029887512, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 216000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 5.9840363e-07, Max: 0.00033901673, Avg: 4.4343953e-05, Variance: 3.1790754e-09
🎯 Economical search: one wavelength ahead 208, derivative adj 2, predicted lag 210, window [190, 139]
⚠️ Invalid economical search range: 190 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.6383825e-07, norm1=1.536059e-06, norm2=1.5269445e-06, result=0.106979296
🔍 Coarse search lag 24: correlation = 0.106979296
🔍 Lag 27: corr=1.3952561e-07, norm1=1.5357327e-06, norm2=1.5267626e-06, result=0.0911193
🔍 Coarse search lag 27: correlation = 0.0911193
🔍 Lag 30: corr=1.6614982e-07, norm1=1.534778e-06, norm2=1.5255139e-06, result=0.10858479
🔍 Coarse search lag 30: correlation = 0.10858479
🔍 Coarse search lag 39: correlation = 0.31997553
🔍 Coarse search lag 42: correlation = 0.4439117
🔍 Coarse search lag 45: correlation = 0.5635199
🔍 Coarse search lag 48: correlation = 0.68378574
🔍 Coarse search lag 51: correlation = 0.7010131
🔍 Coarse search lag 54: correlation = 0.6994231
🔍 Coarse search lag 57: correlation = 0.7034003
🔍 Coarse search lag 60: correlation = 0.60044205
🔍 Coarse search lag 63: correlation = 0.5388251
🔍 Coarse search lag 66: correlation = 0.44276303
🔍 Coarse search lag 69: correlation = 0.3324402
🔍 Coarse search lag 93: correlation = 0.33981282
🔍 Coarse search lag 96: correlation = 0.48548222
🔍 Coarse search lag 99: correlation = 0.5536089
🔍 Coarse search lag 102: correlation = 0.6716015
🔍 Coarse search lag 105: correlation = 0.71300536
🔍 Coarse search lag 108: correlation = 0.653631
🔍 Coarse search lag 111: correlation = 0.7245916
🔍 Coarse search lag 114: correlation = 0.6188152
🔍 Coarse search lag 117: correlation = 0.493435
🔍 Coarse search lag 120: correlation = 0.4251679
🔍 Fine search around lag 111 in range [106, 116]
🔍 Fine search lag 106: correlation = 0.7186278
🔍 Fine search lag 107: correlation = 0.67004305
🔍 Fine search lag 108: correlation = 0.653631
🔍 Fine search lag 109: correlation = 0.6766195
🔍 Fine search lag 110: correlation = 0.71389264
🔍 Fine search lag 111: correlation = 0.7245916
🔍 Fine search lag 112: correlation = 0.671286
🔍 Fine search lag 113: correlation = 0.6248555
🔍 Fine search lag 114: correlation = 0.6188152
🔍 Fine search lag 115: correlation = 0.58726966
🔍 Fine search lag 116: correlation = 0.54018736
🔍 Best correlation: 0.7245916 at period 111 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.0-11.031439 Hz (periods: 88-139)
📊 Moving averages updated - Period: 99.94822, Amplitude: 0.7443231, RunLength: 5
✅ Pitch detected: 8.825151 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 8.825151 Hz
🎵 Detected pitch: 8.825151 Hz (total samples: 216000)
� Calculated pressure: 5.2163444 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 8.825151, Pressure: 5.2163444, Breaths: 0
🎤 Audio Level: 0.035266325, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 220800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 5.9840363e-07, Max: 0.00033901673, Avg: 4.560162e-05, Variance: 3.417998e-09
🎯 Economical search: one wavelength ahead 222, derivative adj 3, predicted lag 225, window [203, 139]
⚠️ Invalid economical search range: 203 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.684189e-07, norm1=1.6473655e-06, norm2=1.5495735e-06, result=0.105411924
🔍 Coarse search lag 24: correlation = 0.105411924
🔍 Lag 27: corr=1.4526917e-07, norm1=1.6300559e-06, norm2=1.5495525e-06, result=0.09140481
🔍 Coarse search lag 27: correlation = 0.09140481
🔍 Lag 30: corr=1.6749695e-07, norm1=1.6075675e-06, norm2=1.5495141e-06, result=0.106126666
🔍 Coarse search lag 30: correlation = 0.106126666
🔍 Coarse search lag 39: correlation = 0.33275256
🔍 Coarse search lag 42: correlation = 0.46800846
🔍 Coarse search lag 45: correlation = 0.58078116
🔍 Coarse search lag 48: correlation = 0.6776361
🔍 Coarse search lag 51: correlation = 0.67172855
🔍 Coarse search lag 54: correlation = 0.7195334
🔍 Coarse search lag 57: correlation = 0.61794925
🔍 Coarse search lag 60: correlation = 0.59852386
🔍 Coarse search lag 63: correlation = 0.4971173
🔍 Coarse search lag 66: correlation = 0.40380153
🔍 Coarse search lag 69: correlation = 0.31482524
🔍 Coarse search lag 90: correlation = 0.3039824
🔍 Coarse search lag 93: correlation = 0.41339475
🔍 Coarse search lag 96: correlation = 0.5015823
🔍 Coarse search lag 99: correlation = 0.5855701
🔍 Coarse search lag 102: correlation = 0.6315759
🔍 Coarse search lag 105: correlation = 0.6879749
🔍 Coarse search lag 108: correlation = 0.6689068
🔍 Coarse search lag 111: correlation = 0.6766145
🔍 Coarse search lag 114: correlation = 0.594814
🔍 Coarse search lag 117: correlation = 0.46052533
🔍 Coarse search lag 120: correlation = 0.34846076
🔍 Fine search around lag 54 in range [49, 59]
🔍 Fine search lag 49: correlation = 0.6847492
🔍 Fine search lag 50: correlation = 0.6631818
🔍 Fine search lag 51: correlation = 0.67172855
🔍 Fine search lag 52: correlation = 0.6968142
🔍 Fine search lag 53: correlation = 0.7150986
🔍 Fine search lag 54: correlation = 0.7195334
🔍 Fine search lag 55: correlation = 0.68435395
🔍 Fine search lag 56: correlation = 0.65594393
🔍 Fine search lag 57: correlation = 0.61794925
🔍 Fine search lag 58: correlation = 0.6170067
🔍 Fine search lag 59: correlation = 0.61384565
🔍 Best correlation: 0.7195334 at period 54 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 13.605443-22.67574 Hz (periods: 43-72)
🔄 Run length reduced: 5 → 1 (leap: 8.339597 Hz, threshold: 1.9601988 Hz)
📊 Moving averages updated - Period: 76.974106, Amplitude: 0.7319282, RunLength: 2
✅ Pitch detected: 18.14059 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 18.14059 Hz
🎵 Detected pitch: 18.14059 Hz (total samples: 220800)
� Calculated pressure: 15.64032 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 18.14059, Pressure: 15.64032, Breaths: 0
🎤 Audio Level: 0.038674954, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 225600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 5.9840363e-07, Max: 0.00032272472, Avg: 4.6122037e-05, Variance: 2.9554301e-09
🎯 Economical search: one wavelength ahead 108, derivative adj -21, predicted lag 87, window [77, 97]
🎯 Economical lag 90: correlation = 0.3258685
🎯 Economical lag 91: correlation = 0.37457284
🎯 Economical lag 92: correlation = 0.41757518
🎯 Economical lag 93: correlation = 0.44744062
🎯 Economical lag 94: correlation = 0.49309388
🎯 Economical lag 95: correlation = 0.5399251
🎯 Economical lag 96: correlation = 0.5675615
🎯 Economical lag 97: correlation = 0.6266771
🔍 Best correlation: 0.6266771 at period 97 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.5741644-12.623607 Hz (periods: 77-129)
🔄 Run length reduced: 2 → 1 (leap: 2.627366 Hz, threshold: 2.5452504 Hz)
📊 Moving averages updated - Period: 86.98705, Amplitude: 0.6793027, RunLength: 2
✅ Pitch detected: 10.098886 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 10.098886 Hz
🎵 Detected pitch: 10.098886 Hz (total samples: 225600)
� Calculated pressure: 6.6416526 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 10.098886, Pressure: 6.6416526, Breaths: 0
🎤 Audio Level: 0.02964677, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 230400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 4.9185485e-07, Max: 0.00032272472, Avg: 4.6943784e-05, Variance: 3.1566971e-09
🎯 Economical search: one wavelength ahead 194, derivative adj 0, predicted lag 194, window [175, 139]
⚠️ Invalid economical search range: 175 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.7755745e-07, norm1=1.3262219e-06, norm2=1.3973507e-06, result=0.13043019
🔍 Coarse search lag 24: correlation = 0.13043019
🔍 Lag 27: corr=1.5439035e-07, norm1=1.3261997e-06, norm2=1.3961572e-06, result=0.113461494
🔍 Coarse search lag 27: correlation = 0.113461494
🔍 Lag 30: corr=1.8176392e-07, norm1=1.3261943e-06, norm2=1.3957061e-06, result=0.1336002
🔍 Coarse search lag 30: correlation = 0.1336002
🔍 Coarse search lag 42: correlation = 0.36448476
🔍 Coarse search lag 45: correlation = 0.4910449
🔍 Coarse search lag 48: correlation = 0.579907
🔍 Coarse search lag 51: correlation = 0.67670983
🔍 Coarse search lag 54: correlation = 0.78060395
🔍 Coarse search lag 57: correlation = 0.68311113
🔍 Coarse search lag 60: correlation = 0.71089816
🔍 Coarse search lag 63: correlation = 0.5834652
🔍 Coarse search lag 66: correlation = 0.47589272
🔍 Coarse search lag 69: correlation = 0.36774617
🔍 Coarse search lag 96: correlation = 0.40223873
🔍 Coarse search lag 99: correlation = 0.50396067
🔍 Coarse search lag 102: correlation = 0.5720398
🔍 Coarse search lag 105: correlation = 0.66278565
🔍 Coarse search lag 108: correlation = 0.7091998
🔍 Coarse search lag 111: correlation = 0.6730177
🔍 Coarse search lag 114: correlation = 0.7197711
🔍 Coarse search lag 117: correlation = 0.6529994
🔍 Coarse search lag 120: correlation = 0.5242897
🔍 Coarse search lag 123: correlation = 0.39988312
🔍 Coarse search lag 126: correlation = 0.3165021
🔍 Fine search around lag 54 in range [49, 59]
🔍 Fine search lag 49: correlation = 0.60992116
🔍 Fine search lag 50: correlation = 0.6668556
🔍 Fine search lag 51: correlation = 0.67670983
🔍 Fine search lag 52: correlation = 0.7142197
🔍 Fine search lag 53: correlation = 0.76415783
🔍 Fine search lag 54: correlation = 0.78060395
🔍 Fine search lag 55: correlation = 0.80179644
🔍 Fine search lag 56: correlation = 0.7364751
🔍 Fine search lag 57: correlation = 0.68311113
🔍 Fine search lag 58: correlation = 0.6580253
🔍 Fine search lag 59: correlation = 0.6809346
🔍 Best correlation: 0.80179644 at period 55 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 13.35807-22.26345 Hz (periods: 44-73)
🔄 Run length reduced: 2 → 0 (leap: 6.549408 Hz, threshold: 2.2522705 Hz)
🔄 Moving averages reset due to run length = 0
✅ Pitch detected: 17.81076 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 17.81076 Hz
🎵 Detected pitch: 17.81076 Hz (total samples: 230400)
� Calculated pressure: 15.27124 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 17.81076, Pressure: 15.27124, Breaths: 0
🎤 Audio Level: 0.02806566, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 235200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 3.0811944e-07, Max: 0.00032272472, Avg: 4.3334254e-05, Variance: 2.9282643e-09
🎯 Economical search: one wavelength ahead 110, derivative adj 0, predicted lag 110, window [99, 121]
🎯 Economical lag 100: correlation = 0.31857792
🎯 Economical lag 101: correlation = 0.3535323
🎯 Economical lag 102: correlation = 0.3909028
🎯 Economical lag 103: correlation = 0.42606863
🎯 Economical lag 104: correlation = 0.4452279
🎯 Economical lag 105: correlation = 0.49584404
🎯 Economical lag 106: correlation = 0.5298386
🎯 Economical lag 107: correlation = 0.57690454
🎯 Economical lag 108: correlation = 0.5905856
🎯 Economical lag 109: correlation = 0.572836
🎯 Economical lag 110: correlation = 0.60056406
🎯 Economical lag 111: correlation = 0.60428876
🎯 Economical lag 112: correlation = 0.64234436
🎯 Economical lag 113: correlation = 0.6829516
🎯 Economical lag 114: correlation = 0.7234766
🎯 Economical lag 115: correlation = 0.7328668
🎯 Economical lag 116: correlation = 0.7280634
🎯 Economical lag 117: correlation = 0.7704543
🎯 Economical lag 118: correlation = 0.7354574
🎯 Economical lag 119: correlation = 0.69979745
🎯 Economical lag 120: correlation = 0.6851936
🎯 Economical lag 121: correlation = 0.6396744
🔍 Best correlation: 0.7704543 at period 117 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-10.465725 Hz (periods: 93-139)
📊 Moving averages updated - Period: 117.0, Amplitude: 0.7704543, RunLength: 1
✅ Pitch detected: 8.37258 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 8.37258 Hz
🎵 Detected pitch: 8.37258 Hz (total samples: 235200)
� Calculated pressure: 4.7099166 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 8.37258, Pressure: 4.7099166, Breaths: 0
🎤 Audio Level: 0.03367214, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 240000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 3.0811944e-07, Max: 0.00027603898, Avg: 4.145801e-05, Variance: 2.9515848e-09
🎯 Economical search: one wavelength ahead 234, derivative adj 0, predicted lag 234, window [211, 139]
⚠️ Invalid economical search range: 211 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.4125168e-07, norm1=1.3768481e-06, norm2=1.392187e-06, result=0.10202388
🔍 Coarse search lag 24: correlation = 0.10202388
🔍 Lag 27: corr=1.0347472e-07, norm1=1.3656403e-06, norm2=1.3915798e-06, result=0.075060606
🔍 Coarse search lag 27: correlation = 0.075060606
🔍 Lag 30: corr=1.055359e-07, norm1=1.3494044e-06, norm2=1.3866511e-06, result=0.0771517
🔍 Coarse search lag 30: correlation = 0.0771517
🔍 Coarse search lag 45: correlation = 0.40649372
🔍 Coarse search lag 48: correlation = 0.50893486
🔍 Coarse search lag 51: correlation = 0.64968044
🔍 Coarse search lag 54: correlation = 0.71540165
🔍 Coarse search lag 57: correlation = 0.7690936
🔍 Coarse search lag 60: correlation = 0.7705583
🔍 Coarse search lag 63: correlation = 0.69900775
🔍 Coarse search lag 66: correlation = 0.615394
🔍 Coarse search lag 69: correlation = 0.51344967
🔍 Coarse search lag 72: correlation = 0.38709792
🔍 Coarse search lag 102: correlation = 0.31608015
🔍 Coarse search lag 105: correlation = 0.43374553
🔍 Coarse search lag 108: correlation = 0.5783568
🔍 Coarse search lag 111: correlation = 0.66580504
🔍 Coarse search lag 114: correlation = 0.6805943
🔍 Coarse search lag 117: correlation = 0.7589908
🔍 Coarse search lag 120: correlation = 0.71754676
🔍 Coarse search lag 123: correlation = 0.68483955
🔍 Coarse search lag 126: correlation = 0.6116797
🔍 Coarse search lag 129: correlation = 0.43964306
🔍 Coarse search lag 132: correlation = 0.30592626
🔍 Fine search around lag 60 in range [55, 65]
🔍 Fine search lag 55: correlation = 0.7432219
🔍 Fine search lag 56: correlation = 0.7627111
🔍 Fine search lag 57: correlation = 0.7690936
🔍 Fine search lag 58: correlation = 0.76147956
🔍 Fine search lag 59: correlation = 0.7567608
🔍 Fine search lag 60: correlation = 0.7705583
🔍 Fine search lag 61: correlation = 0.7558214
🔍 Fine search lag 62: correlation = 0.7612052
🔍 Fine search lag 63: correlation = 0.69900775
🔍 Fine search lag 64: correlation = 0.68180865
🔍 Fine search lag 65: correlation = 0.67080355
🔍 Best correlation: 0.7705583 at period 60 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 12.244898-20.408163 Hz (periods: 48-80)
🔄 Run length reduced: 1 → 0 (leap: 7.953951 Hz, threshold: 1.674516 Hz)
🔄 Moving averages reset due to run length = 0
✅ Pitch detected: 16.32653 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 16.32653 Hz
🎵 Detected pitch: 16.32653 Hz (total samples: 240000)
� Calculated pressure: 13.610386 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 16.32653, Pressure: 13.610386, Breaths: 0
🎤 Audio Level: 0.028400801, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 244800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.8119879e-07, Max: 0.00025651374, Avg: 3.8218728e-05, Variance: 2.5264673e-09
🎯 Economical search: one wavelength ahead 120, derivative adj 0, predicted lag 120, window [108, 132]
🎯 Economical lag 108: correlation = 0.6511447
🎯 Economical lag 109: correlation = 0.68579525
🎯 Economical lag 110: correlation = 0.7220917
🎯 Economical lag 111: correlation = 0.73485905
🎯 Economical lag 112: correlation = 0.7146769
🎯 Economical lag 113: correlation = 0.7192915
🎯 Economical lag 114: correlation = 0.6949075
🎯 Economical lag 115: correlation = 0.6729512
🎯 Economical lag 116: correlation = 0.6893834
🎯 Economical lag 117: correlation = 0.7057322
🎯 Economical lag 118: correlation = 0.68948215
🎯 Economical lag 119: correlation = 0.6318557
🎯 Economical lag 120: correlation = 0.58000547
🎯 Economical lag 121: correlation = 0.56882536
🎯 Economical lag 122: correlation = 0.5672354
🎯 Economical lag 123: correlation = 0.55840343
🎯 Economical lag 124: correlation = 0.52844733
🎯 Economical lag 125: correlation = 0.49536926
🎯 Economical lag 126: correlation = 0.45810336
🎯 Economical lag 127: correlation = 0.393421
🔍 Best correlation: 0.73485905 at period 111 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-11.031439 Hz (periods: 88-139)
📊 Moving averages updated - Period: 111.0, Amplitude: 0.73485905, RunLength: 1
✅ Pitch detected: 8.825151 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 8.825151 Hz
🎵 Detected pitch: 8.825151 Hz (total samples: 244800)
� Calculated pressure: 5.2163444 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 8.825151, Pressure: 5.2163444, Breaths: 0
🎤 Audio Level: 0.017856114, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 249600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 3.9511722e-08, Max: 0.00025546565, Avg: 2.9851968e-05, Variance: 1.775058e-09
🎯 Economical search: one wavelength ahead 222, derivative adj 0, predicted lag 222, window [200, 139]
⚠️ Invalid economical search range: 200 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=6.5605825e-08, norm1=7.996491e-07, norm2=6.672771e-07, result=0.08981307
🔍 Coarse search lag 24: correlation = 0.08981307
🔍 Lag 27: corr=4.4632863e-08, norm1=7.9724765e-07, norm2=6.351946e-07, result=0.06271981
🔍 Coarse search lag 27: correlation = 0.06271981
🔍 Lag 30: corr=3.931936e-08, norm1=7.874382e-07, norm2=5.8324326e-07, result=0.058019415
🔍 Coarse search lag 30: correlation = 0.058019415
🔍 Coarse search lag 45: correlation = 0.3488324
🔍 Coarse search lag 48: correlation = 0.4278207
🔍 Coarse search lag 51: correlation = 0.5850083
🔍 Coarse search lag 54: correlation = 0.5905544
🔍 Coarse search lag 57: correlation = 0.6449523
🔍 Coarse search lag 60: correlation = 0.6787356
🔍 Coarse search lag 63: correlation = 0.6063306
🔍 Coarse search lag 66: correlation = 0.58263296
🔍 Coarse search lag 69: correlation = 0.4626947
🔍 Coarse search lag 72: correlation = 0.34853935
🔍 Coarse search lag 75: correlation = 0.30987656
🔍 Coarse search lag 102: correlation = 0.30469453
🔍 Coarse search lag 105: correlation = 0.44963002
🔍 Coarse search lag 108: correlation = 0.48882386
🔍 Coarse search lag 111: correlation = 0.58575493
🔍 Coarse search lag 114: correlation = 0.6526849
🔍 Coarse search lag 117: correlation = 0.60354346
🔍 Coarse search lag 120: correlation = 0.5644061
🔍 Coarse search lag 123: correlation = 0.6233048
🔍 Coarse search lag 126: correlation = 0.521371
🔍 Coarse search lag 129: correlation = 0.3895417
🔍 Coarse search lag 132: correlation = 0.40946123
🔍 Coarse search lag 135: correlation = 0.35267806
🔍 Fine search around lag 60 in range [55, 65]
🔍 Fine search lag 55: correlation = 0.589265
🔍 Fine search lag 56: correlation = 0.6244725
🔍 Fine search lag 57: correlation = 0.6449523
🔍 Fine search lag 58: correlation = 0.6488784
🔍 Fine search lag 59: correlation = 0.66476107
🔍 Fine search lag 60: correlation = 0.6787356
🔍 Fine search lag 61: correlation = 0.6745125
🔍 Fine search lag 62: correlation = 0.64383346
🔍 Fine search lag 63: correlation = 0.6063306
🔍 Fine search lag 64: correlation = 0.60856986
🔍 Fine search lag 65: correlation = 0.61375034
🔍 Best correlation: 0.6787356 at period 60 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 12.244898-20.408163 Hz (periods: 48-80)
🔄 Run length reduced: 1 → 0 (leap: 7.501379 Hz, threshold: 1.7650303 Hz)
🔄 Moving averages reset due to run length = 0
✅ Pitch detected: 16.32653 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 16.32653 Hz
🎵 Detected pitch: 16.32653 Hz (total samples: 249600)
� Calculated pressure: 13.610386 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 16.32653, Pressure: 13.610386, Breaths: 0
🎤 Audio Level: 0.019232329, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 254400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 3.9511722e-08, Max: 0.00020965692, Avg: 2.4171837e-05, Variance: 1.220151e-09
🎯 Economical search: one wavelength ahead 120, derivative adj 0, predicted lag 120, window [108, 132]
🎯 Economical lag 112: correlation = 0.3031617
🎯 Economical lag 113: correlation = 0.33471185
🎯 Economical lag 114: correlation = 0.36068535
🎯 Economical lag 115: correlation = 0.39060715
🎯 Economical lag 116: correlation = 0.45030138
🎯 Economical lag 117: correlation = 0.50178874
🎯 Economical lag 118: correlation = 0.5472207
🎯 Economical lag 119: correlation = 0.6248049
🎯 Economical lag 120: correlation = 0.6691618
🎯 Economical lag 121: correlation = 0.66968745
🎯 Economical lag 122: correlation = 0.64234954
🎯 Economical lag 123: correlation = 0.6406511
🎯 Economical lag 124: correlation = 0.6576779
🎯 Economical lag 125: correlation = 0.6802186
🎯 Economical lag 126: correlation = 0.6978541
🎯 Economical lag 127: correlation = 0.66927594
🎯 Economical lag 128: correlation = 0.6420226
🎯 Economical lag 129: correlation = 0.66583526
🎯 Economical lag 130: correlation = 0.70449024
🎯 Economical lag 131: correlation = 0.71229714
🎯 Economical lag 132: correlation = 0.6940773
🔍 Best correlation: 0.71229714 at period 131 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-9.34725 Hz (periods: 104-139)
📊 Moving averages updated - Period: 131.0, Amplitude: 0.71229714, RunLength: 1
✅ Pitch detected: 7.4778004 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.4778004 Hz
🎵 Detected pitch: 7.4778004 Hz (total samples: 254400)
� Calculated pressure: 3.7086587 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.4778004, Pressure: 3.7086587, Breaths: 0
🎤 Audio Level: 0.013313683, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 259200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 3.9511722e-08, Max: 0.00018587823, Avg: 1.8979996e-05, Variance: 8.3744806e-10
🎯 Economical search: one wavelength ahead 262, derivative adj 0, predicted lag 262, window [236, 139]
⚠️ Invalid economical search range: 236 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=3.707043e-08, norm1=3.5870852e-07, norm2=2.541427e-07, result=0.12277727
🔍 Coarse search lag 24: correlation = 0.12277727
🔍 Lag 27: corr=2.3709676e-08, norm1=3.584936e-07, norm2=2.5412902e-07, result=0.0785521
🔍 Coarse search lag 27: correlation = 0.0785521
🔍 Lag 30: corr=1.5206993e-08, norm1=3.5668972e-07, norm2=2.5412243e-07, result=0.050509907
🔍 Coarse search lag 30: correlation = 0.050509907
🔍 Coarse search lag 48: correlation = 0.3009023
🔍 Coarse search lag 51: correlation = 0.42030254
🔍 Coarse search lag 54: correlation = 0.47523075
🔍 Coarse search lag 57: correlation = 0.48579684
🔍 Coarse search lag 60: correlation = 0.5295415
🔍 Coarse search lag 63: correlation = 0.53958654
🔍 Coarse search lag 66: correlation = 0.67742026
🔍 Coarse search lag 69: correlation = 0.71163595
🔍 Coarse search lag 72: correlation = 0.6349719
🔍 Coarse search lag 75: correlation = 0.5729322
🔍 Coarse search lag 78: correlation = 0.3919669
🔍 Coarse search lag 117: correlation = 0.43294454
🔍 Coarse search lag 120: correlation = 0.61883336
🔍 Coarse search lag 123: correlation = 0.60944
🔍 Coarse search lag 126: correlation = 0.711738
🔍 Coarse search lag 129: correlation = 0.6274757
🔍 Coarse search lag 132: correlation = 0.57782084
🔍 Coarse search lag 135: correlation = 0.59345156
🔍 Coarse search lag 138: correlation = 0.61467093
🔍 Fine search around lag 126 in range [121, 131]
🔍 Fine search lag 121: correlation = 0.61626625
🔍 Fine search lag 122: correlation = 0.5896192
🔍 Fine search lag 123: correlation = 0.60944
🔍 Fine search lag 124: correlation = 0.646532
🔍 Fine search lag 125: correlation = 0.67880666
🔍 Fine search lag 126: correlation = 0.711738
🔍 Fine search lag 127: correlation = 0.7031579
🔍 Fine search lag 128: correlation = 0.63141555
🔍 Fine search lag 129: correlation = 0.6274757
🔍 Fine search lag 130: correlation = 0.6563477
🔍 Fine search lag 131: correlation = 0.63474435
🔍 Best correlation: 0.711738 at period 126 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.0-9.718173 Hz (periods: 100-139)
📊 Moving averages updated - Period: 128.5, Amplitude: 0.71201754, RunLength: 2
✅ Pitch detected: 7.7745385 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.7745385 Hz
🎵 Detected pitch: 7.7745385 Hz (total samples: 259200)
� Calculated pressure: 4.040708 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.7745385, Pressure: 4.040708, Breaths: 0
🎤 Audio Level: 0.012267372, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 264000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 4.2336396e-08, Max: 0.00015220937, Avg: 1.3182981e-05, Variance: 4.9148846e-10
🎯 Economical search: one wavelength ahead 252, derivative adj -2, predicted lag 250, window [225, 139]
⚠️ Invalid economical search range: 225 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=2.4640702e-08, norm1=1.9958162e-07, norm2=1.9590526e-07, result=0.12461484
🔍 Coarse search lag 24: correlation = 0.12461484
🔍 Lag 27: corr=1.5874205e-08, norm1=1.9958146e-07, norm2=1.7074628e-07, result=0.08599164
🔍 Coarse search lag 27: correlation = 0.08599164
🔍 Lag 30: corr=9.379193e-09, norm1=1.9957773e-07, norm2=1.3680494e-07, result=0.056762114
🔍 Coarse search lag 30: correlation = 0.056762114
🔍 Coarse search lag 57: correlation = 0.38109624
🔍 Coarse search lag 60: correlation = 0.44783747
🔍 Coarse search lag 63: correlation = 0.49270123
🔍 Coarse search lag 66: correlation = 0.6573637
🔍 Coarse search lag 69: correlation = 0.7144573
🔍 Coarse search lag 72: correlation = 0.7046544
🔍 Coarse search lag 75: correlation = 0.7000297
🔍 Coarse search lag 78: correlation = 0.5161964
🔍 Coarse search lag 81: correlation = 0.36852786
🔍 Coarse search lag 123: correlation = 0.35216466
🔍 Coarse search lag 126: correlation = 0.3987109
🔍 Coarse search lag 129: correlation = 0.510734
🔍 Coarse search lag 132: correlation = 0.5868591
🔍 Coarse search lag 135: correlation = 0.6625719
🔍 Coarse search lag 138: correlation = 0.65020686
🔍 Fine search around lag 69 in range [64, 74]
🔍 Fine search lag 64: correlation = 0.52656305
🔍 Fine search lag 65: correlation = 0.6166037
🔍 Fine search lag 66: correlation = 0.6573637
🔍 Fine search lag 67: correlation = 0.69661117
🔍 Fine search lag 68: correlation = 0.7179819
🔍 Fine search lag 69: correlation = 0.7144573
🔍 Fine search lag 70: correlation = 0.6898442
🔍 Fine search lag 71: correlation = 0.6869758
🔍 Fine search lag 72: correlation = 0.7046544
🔍 Fine search lag 73: correlation = 0.74096555
🔍 Fine search lag 74: correlation = 0.74309564
🔍 Best correlation: 0.74309564 at period 74 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 9.928296-16.547161 Hz (periods: 59-98)
🔄 Run length reduced: 2 → 0 (leap: 5.614445 Hz, threshold: 1.5246567 Hz)
🔄 Moving averages reset due to run length = 0
✅ Pitch detected: 13.237728 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 13.237728 Hz
🎵 Detected pitch: 13.237728 Hz (total samples: 264000)
� Calculated pressure: 10.1540165 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 13.237728, Pressure: 10.1540165, Breaths: 0
🎤 Audio Level: 0.010771665, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 268800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 2.7825594e-08, Max: 0.00015220937, Avg: 9.036751e-06, Variance: 2.4266777e-10
🎯 Economical search: one wavelength ahead 148, derivative adj 0, predicted lag 148, window [134, 139]
🎯 Economical lag 134: correlation = 0.46561348
🎯 Economical lag 135: correlation = 0.46787578
🎯 Economical lag 136: correlation = 0.43671212
🎯 Economical lag 137: correlation = 0.43296847
🎯 Economical lag 138: correlation = 0.44320288
🎯 Economical lag 139: correlation = 0.45988208
🔄 Economical search failed (best: 0.46787578), falling back to full search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.0728284e-08, norm1=9.713027e-08, norm2=4.500033e-08, result=0.16227257
🔍 Coarse search lag 24: correlation = 0.16227257
🔍 Lag 27: corr=6.9390502e-09, norm1=9.7130254e-08, norm2=4.4899135e-08, result=0.10507607
🔍 Coarse search lag 27: correlation = 0.10507607
🔍 Lag 30: corr=5.6770966e-09, norm1=9.713025e-08, norm2=4.4857796e-08, result=0.086006254
🔍 Coarse search lag 30: correlation = 0.086006254
🔍 Coarse search lag 57: correlation = 0.43740204
🔍 Coarse search lag 60: correlation = 0.41882545
🔍 Coarse search lag 63: correlation = 0.500735
🔍 Coarse search lag 66: correlation = 0.5753338
🔍 Coarse search lag 69: correlation = 0.7086793
🔍 Coarse search lag 72: correlation = 0.58490133
🔍 Coarse search lag 75: correlation = 0.58165854
🔍 Coarse search lag 78: correlation = 0.55365616
🔍 Coarse search lag 81: correlation = 0.47232872
🔍 Coarse search lag 84: correlation = 0.4214754
🔍 Coarse search lag 87: correlation = 0.41001475
🔍 Coarse search lag 90: correlation = 0.3266155
🔍 Coarse search lag 129: correlation = 0.31938702
🔍 Coarse search lag 132: correlation = 0.40780666
🔍 Coarse search lag 135: correlation = 0.46787578
🔍 Coarse search lag 138: correlation = 0.44320288
🔍 Fine search around lag 69 in range [64, 74]
🔍 Fine search lag 64: correlation = 0.4833982
🔍 Fine search lag 65: correlation = 0.51216686
🔍 Fine search lag 66: correlation = 0.5753338
🔍 Fine search lag 67: correlation = 0.6483891
🔍 Fine search lag 68: correlation = 0.67632
🔍 Fine search lag 69: correlation = 0.7086793
🔍 Fine search lag 70: correlation = 0.68872637
🔍 Fine search lag 71: correlation = 0.596947
🔍 Fine search lag 72: correlation = 0.58490133
🔍 Fine search lag 73: correlation = 0.6448693
🔍 Fine search lag 74: correlation = 0.6184351
🔍 Best correlation: 0.7086793 at period 69 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 10.6477375-17.74623 Hz (periods: 55-92)
📊 Moving averages updated - Period: 69.0, Amplitude: 0.7086793, RunLength: 1
✅ Pitch detected: 14.196983 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 14.196983 Hz
🎵 Detected pitch: 14.196983 Hz (total samples: 268800)
� Calculated pressure: 11.227425 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 14.196983, Pressure: 11.227425, Breaths: 0
🎤 Audio Level: 0.010535774, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 273600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 5.6568226e-09, Max: 4.9751103e-05, Avg: 5.885315e-06, Variance: 7.557472e-11
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 273600)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0019745927, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 278400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 5.6568226e-09, Max: 4.182487e-05, Avg: 3.6640306e-06, Variance: 4.1958333e-11
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 278400)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0031189262, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 283200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 5.6568226e-09, Max: 4.182487e-05, Avg: 2.0259151e-06, Variance: 2.331229e-11
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 283200)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.002747305, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 288000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 5.6568226e-09, Max: 2.114585e-06, Avg: 2.851126e-07, Variance: 1.3899571e-13
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 288000)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🫁 Breath detection: Entering ending state (silence: 0.3s)
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 0
🎤 Audio Level: 0.0023284692, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 292800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.44459245e-08, Max: 2.114585e-06, Avg: 3.1662697e-07, Variance: 1.2994305e-13
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 292800)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🫁 Breath detection: Completed breath #1 (duration: 2.9s)
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.0015059944, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 297600
🫁 Recorded breath duration: 2.9s (Total: 1)
🫁 Breath completed for step 1: duration=2.9s, performance=completedAmber
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.2049835e-08, Max: 7.838727e-07, Avg: 1.4815268e-07, Variance: 1.9409352e-14
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 297600)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.0011591562, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 302400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 4.235914e-09, Max: 7.059198e-07, Avg: 1.05869596e-07, Variance: 1.1171022e-14
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 302400)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.0007312204, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 307200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 4.235914e-09, Max: 5.1692086e-07, Avg: 6.606147e-08, Variance: 5.764024e-15
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 307200)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.0007944838, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 312000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 4.235914e-09, Max: 2.0589376e-07, Avg: 3.4386087e-08, Variance: 7.845334e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 312000)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.00091789645, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 316800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 4.4249675e-09, Max: 1.3254055e-07, Avg: 3.3632755e-08, Variance: 5.128093e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 316800)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.0016883513, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 321600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 5.7468608e-09, Max: 4.6764558e-07, Avg: 4.7955577e-08, Variance: 2.0583178e-15
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 321600)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.0007435895, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 326400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 8.544128e-09, Max: 4.6764558e-07, Avg: 5.3126957e-08, Variance: 2.0244759e-15
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 326400)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.00072990014, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 331200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 6.4615597e-09, Max: 4.6764558e-07, Avg: 5.26096e-08, Variance: 2.0092424e-15
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 331200)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.0005821877, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 336000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.4229019e-09, Max: 1.3317943e-07, Avg: 3.521024e-08, Variance: 7.178692e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 336000)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.004244177, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 340800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.4229019e-09, Max: 3.9837305e-06, Avg: 4.3693042e-08, Variance: 5.61507e-14
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 340800)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.024518993, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 345600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.4229019e-09, Max: 0.00019545316, Avg: 8.519291e-06, Variance: 7.15177e-10
🎯 Economical search: one wavelength ahead 138, derivative adj 0, predicted lag 138, window [125, 139]
🎯 Economical lag 132: correlation = 0.34736204
🎯 Economical lag 133: correlation = 0.5400984
🎯 Economical lag 134: correlation = 0.5155874
🎯 Economical lag 135: correlation = 0.45780492
🎯 Economical lag 137: correlation = 0.30519477
🔄 Economical search failed (best: 0.5400984), falling back to full search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=4.0085332e-08, norm1=2.3351397e-07, norm2=2.3632657e-07, result=0.17063683
🔍 Coarse search lag 24: correlation = 0.17063683
🔍 Lag 27: corr=3.2058452e-08, norm1=2.2197135e-07, norm2=2.3632657e-07, result=0.13997091
🔍 Coarse search lag 27: correlation = 0.13997091
🔍 Lag 30: corr=3.283949e-08, norm1=2.1808914e-07, norm2=2.3632657e-07, result=0.14465156
🔍 Coarse search lag 30: correlation = 0.14465156
🔍 Coarse search lag 132: correlation = 0.34736204
🔍 Coarse search lag 135: correlation = 0.45780492
🔍 Fine search around lag 135 in range [130, 139]
🔍 Fine search lag 132: correlation = 0.34736204
🔍 Fine search lag 133: correlation = 0.5400984
🔍 Fine search lag 134: correlation = 0.5155874
🔍 Fine search lag 135: correlation = 0.45780492
🔍 Fine search lag 137: correlation = 0.30519477
🔍 Best correlation: 0.5400984 at period 133 (threshold: 0.6) [strategy: fallback]
❌ No pitch detected - best correlation 0.5400984 below threshold 0.6
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 345600)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🫁 Breath detection: Starting new breath
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.041142352, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 350400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 2.6081957e-09, Max: 0.00039684825, Avg: 3.2966436e-05, Variance: 4.0088333e-09
🎯 Economical search: one wavelength ahead 138, derivative adj 0, predicted lag 138, window [125, 139]
🔄 Economical search failed (best: 0.14773954), falling back to full search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.3778053e-07, norm1=1.4795017e-06, norm2=1.5286859e-06, result=0.09161592
🔍 Coarse search lag 24: correlation = 0.09161592
🔍 Lag 27: corr=1.3210887e-07, norm1=1.4695341e-06, norm2=1.5286689e-06, result=0.088142514
🔍 Coarse search lag 27: correlation = 0.088142514
🔍 Lag 30: corr=1.5162087e-07, norm1=1.3799754e-06, norm2=1.5286689e-06, result=0.10439185
🔍 Coarse search lag 30: correlation = 0.10439185
🔍 Coarse search lag 39: correlation = 0.31816596
🔍 Coarse search lag 42: correlation = 0.42636183
🔍 Coarse search lag 45: correlation = 0.53564477
🔍 Coarse search lag 48: correlation = 0.6407019
🔍 Coarse search lag 51: correlation = 0.7433565
🔍 Coarse search lag 54: correlation = 0.68494165
🔍 Coarse search lag 57: correlation = 0.7037224
🔍 Coarse search lag 60: correlation = 0.6141353
🔍 Coarse search lag 63: correlation = 0.48558384
🔍 Coarse search lag 66: correlation = 0.38242024
🔍 Coarse search lag 96: correlation = 0.302475
🔍 Coarse search lag 99: correlation = 0.36629707
🔍 Coarse search lag 102: correlation = 0.3753401
🔍 Coarse search lag 105: correlation = 0.5132886
🔍 Coarse search lag 108: correlation = 0.5648454
🔍 Coarse search lag 111: correlation = 0.43843114
🔍 Coarse search lag 114: correlation = 0.3219612
🔍 Coarse search lag 117: correlation = 0.40554914
🔍 Coarse search lag 120: correlation = 0.35041597
🔍 Fine search around lag 51 in range [46, 56]
🔍 Fine search lag 46: correlation = 0.5919063
🔍 Fine search lag 47: correlation = 0.6445711
🔍 Fine search lag 48: correlation = 0.6407019
🔍 Fine search lag 49: correlation = 0.6425968
🔍 Fine search lag 50: correlation = 0.6988226
🔍 Fine search lag 51: correlation = 0.7433565
🔍 Fine search lag 52: correlation = 0.7433249
🔍 Fine search lag 53: correlation = 0.71767354
🔍 Fine search lag 54: correlation = 0.68494165
🔍 Fine search lag 55: correlation = 0.7043398
🔍 Fine search lag 56: correlation = 0.70868635
🔍 Best correlation: 0.7433565 at period 51 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 14.405763-24.009605 Hz (periods: 40-68)
🔄 Run length reduced: 1 → 0 (leap: 5.0107 Hz, threshold: 2.8393967 Hz)
🔄 Moving averages reset due to run length = 0
✅ Pitch detected: 19.207684 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 19.207684 Hz
🎵 Detected pitch: 19.207684 Hz (total samples: 350400)
� Calculated pressure: 16.834396 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 19.207684, Pressure: 16.834396, Breaths: 1
🎤 Audio Level: 0.041792512, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 355200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 2.6848537e-09, Max: 0.0005085148, Avg: 6.99945e-05, Variance: 8.774156e-09
🎯 Economical search: one wavelength ahead 102, derivative adj 0, predicted lag 102, window [92, 112]
🎯 Economical lag 92: correlation = 0.52131456
🎯 Economical lag 93: correlation = 0.53220344
🎯 Economical lag 94: correlation = 0.55595315
🎯 Economical lag 95: correlation = 0.57745725
🎯 Economical lag 96: correlation = 0.6014495
🎯 Economical lag 97: correlation = 0.5962826
🎯 Economical lag 98: correlation = 0.58632994
🎯 Economical lag 99: correlation = 0.5825909
🎯 Economical lag 100: correlation = 0.6038534
🎯 Economical lag 101: correlation = 0.6066396
🎯 Economical lag 102: correlation = 0.6167575
🎯 Economical lag 103: correlation = 0.6110098
🎯 Economical lag 104: correlation = 0.588554
🎯 Economical lag 105: correlation = 0.58190924
🎯 Economical lag 106: correlation = 0.57342714
🎯 Economical lag 107: correlation = 0.5681404
🎯 Economical lag 108: correlation = 0.5496363
🎯 Economical lag 109: correlation = 0.51482457
🎯 Economical lag 110: correlation = 0.48531696
🎯 Economical lag 111: correlation = 0.45666486
🎯 Economical lag 112: correlation = 0.4143925
🔍 Best correlation: 0.6167575 at period 102 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.2028813-12.004803 Hz (periods: 81-136)
📊 Moving averages updated - Period: 102.0, Amplitude: 0.6167575, RunLength: 1
✅ Pitch detected: 9.603842 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.603842 Hz
🎵 Detected pitch: 9.603842 Hz (total samples: 355200)
� Calculated pressure: 6.0876985 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.603842, Pressure: 6.0876985, Breaths: 1
🎤 Audio Level: 0.052061617, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 360000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 2.2063989e-07, Max: 0.0007048571, Avg: 0.00010445652, Variance: 1.5553274e-08
🎯 Economical search: one wavelength ahead 204, derivative adj 0, predicted lag 204, window [184, 139]
⚠️ Invalid economical search range: 184 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.331784e-06, norm1=7.375149e-06, norm2=7.540139e-06, result=0.17859066
🔍 Coarse search lag 24: correlation = 0.17859066
🔍 Lag 27: corr=1.5536486e-06, norm1=7.370765e-06, norm2=7.4005816e-06, result=0.2103602
🔍 Coarse search lag 27: correlation = 0.2103602
🔍 Lag 30: corr=1.9572915e-06, norm1=7.3653296e-06, norm2=7.3871893e-06, result=0.26535043
🔍 Coarse search lag 30: correlation = 0.26535043
🔍 Coarse search lag 33: correlation = 0.36423388
🔍 Coarse search lag 36: correlation = 0.4850363
🔍 Coarse search lag 39: correlation = 0.5304968
🔍 Coarse search lag 42: correlation = 0.6351028
🔍 Coarse search lag 45: correlation = 0.7130023
🔍 Coarse search lag 48: correlation = 0.69870675
🔍 Coarse search lag 51: correlation = 0.7137951
🔍 Coarse search lag 54: correlation = 0.5580321
🔍 Coarse search lag 57: correlation = 0.40381882
🔍 Coarse search lag 60: correlation = 0.30614415
🔍 Coarse search lag 78: correlation = 0.30799696
🔍 Coarse search lag 81: correlation = 0.40603948
🔍 Coarse search lag 84: correlation = 0.601988
🔍 Coarse search lag 87: correlation = 0.6842644
🔍 Coarse search lag 90: correlation = 0.6471813
🔍 Coarse search lag 93: correlation = 0.63538116
🔍 Coarse search lag 96: correlation = 0.6381498
🔍 Coarse search lag 99: correlation = 0.5331685
🔍 Coarse search lag 102: correlation = 0.49678034
🔍 Coarse search lag 105: correlation = 0.38785753
🔍 Coarse search lag 108: correlation = 0.30225837
🔍 Coarse search lag 126: correlation = 0.3371868
🔍 Coarse search lag 129: correlation = 0.52201277
🔍 Coarse search lag 132: correlation = 0.5584532
🔍 Coarse search lag 135: correlation = 0.68009543
🔍 Coarse search lag 138: correlation = 0.7638783
🔍 Fine search around lag 138 in range [133, 139]
🔍 Fine search lag 133: correlation = 0.6022408
🔍 Fine search lag 134: correlation = 0.6330754
🔍 Fine search lag 135: correlation = 0.68009543
🔍 Fine search lag 136: correlation = 0.7308873
🔍 Fine search lag 137: correlation = 0.7463443
🔍 Fine search lag 138: correlation = 0.7638783
🔍 Fine search lag 139: correlation = 0.73321754
🔍 Best correlation: 0.7638783 at period 138 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.0-8.873115 Hz (periods: 110-139)
🔄 Run length reduced: 1 → 0 (leap: 2.50535 Hz, threshold: 1.9207684 Hz)
🔄 Moving averages reset due to run length = 0
✅ Pitch detected: 7.0984917 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.0984917 Hz
🎵 Detected pitch: 7.0984917 Hz (total samples: 360000)
� Calculated pressure: 3.284212 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.0984917, Pressure: 3.284212, Breaths: 1
🎤 Audio Level: 0.069293946, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 364800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 2.2063989e-07, Max: 0.0014113629, Avg: 0.00013263333, Variance: 2.7702946e-08
🎯 Economical search: one wavelength ahead 276, derivative adj 0, predicted lag 276, window [249, 139]
⚠️ Invalid economical search range: 249 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=2.608106e-06, norm1=1.281516e-05, norm2=1.3333925e-05, result=0.19951898
🔍 Coarse search lag 24: correlation = 0.19951898
🔍 Lag 27: corr=2.6383268e-06, norm1=1.2810999e-05, norm2=1.32714695e-05, result=0.20233805
🔍 Coarse search lag 27: correlation = 0.20233805
🔍 Lag 30: corr=3.347583e-06, norm1=1.2806301e-05, norm2=1.2818515e-05, result=0.2612767
🔍 Coarse search lag 30: correlation = 0.2612767
🔍 Coarse search lag 33: correlation = 0.3690936
🔍 Coarse search lag 36: correlation = 0.4498339
🔍 Coarse search lag 39: correlation = 0.49616238
🔍 Coarse search lag 42: correlation = 0.57171965
🔍 Coarse search lag 45: correlation = 0.62039787
🔍 Coarse search lag 48: correlation = 0.5991392
🔍 Coarse search lag 51: correlation = 0.54752934
🔍 Coarse search lag 54: correlation = 0.45565137
🔍 Coarse search lag 57: correlation = 0.3271739
🔍 Coarse search lag 78: correlation = 0.3287183
🔍 Coarse search lag 81: correlation = 0.4755512
🔍 Coarse search lag 84: correlation = 0.61549354
🔍 Coarse search lag 87: correlation = 0.7569711
🔍 Coarse search lag 90: correlation = 0.6194941
🔍 Coarse search lag 93: correlation = 0.57653815
🔍 Coarse search lag 96: correlation = 0.5991486
🔍 Coarse search lag 99: correlation = 0.46553692
🔍 Coarse search lag 102: correlation = 0.31869492
🔍 Coarse search lag 123: correlation = 0.36457574
🔍 Coarse search lag 126: correlation = 0.40228218
🔍 Coarse search lag 129: correlation = 0.568438
🔍 Coarse search lag 132: correlation = 0.7688864
🔍 Coarse search lag 135: correlation = 0.7053112
🔍 Coarse search lag 138: correlation = 0.6366423
🔍 Fine search around lag 132 in range [127, 137]
🔍 Fine search lag 127: correlation = 0.43471053
🔍 Fine search lag 128: correlation = 0.49227333
🔍 Fine search lag 129: correlation = 0.568438
🔍 Fine search lag 130: correlation = 0.6599827
🔍 Fine search lag 131: correlation = 0.7360913
🔍 Fine search lag 132: correlation = 0.7688864
🔍 Fine search lag 133: correlation = 0.7898016
🔍 Fine search lag 134: correlation = 0.74065346
🔍 Fine search lag 135: correlation = 0.7053112
🔍 Fine search lag 136: correlation = 0.6866955
🔍 Fine search lag 137: correlation = 0.65393347
🔍 Best correlation: 0.7898016 at period 133 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.0-9.20669 Hz (periods: 106-139)
📊 Moving averages updated - Period: 133.0, Amplitude: 0.7898016, RunLength: 1
✅ Pitch detected: 7.365352 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.365352 Hz
🎵 Detected pitch: 7.365352 Hz (total samples: 364800)
� Calculated pressure: 3.582829 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.365352, Pressure: 3.582829, Breaths: 1
🎤 Audio Level: 0.04322317, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 369600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 2.4678232e-06, Max: 0.0014113629, Avg: 0.00013799893, Variance: 2.8762702e-08
🎯 Economical search: one wavelength ahead 266, derivative adj 0, predicted lag 266, window [240, 139]
⚠️ Invalid economical search range: 240 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=2.5403456e-06, norm1=1.2893073e-05, norm2=1.250265e-05, result=0.20008454
🔍 Coarse search lag 24: correlation = 0.20008454
🔍 Lag 27: corr=2.5610157e-06, norm1=1.2675183e-05, norm2=1.2436949e-05, result=0.20397559
🔍 Coarse search lag 27: correlation = 0.20397559
🔍 Lag 30: corr=3.2138084e-06, norm1=1.2665802e-05, norm2=1.2423346e-05, result=0.2562031
🔍 Coarse search lag 30: correlation = 0.2562031
🔍 Coarse search lag 33: correlation = 0.36743543
🔍 Coarse search lag 36: correlation = 0.45379713
🔍 Coarse search lag 39: correlation = 0.5062896
🔍 Coarse search lag 42: correlation = 0.60844374
🔍 Coarse search lag 45: correlation = 0.63743913
🔍 Coarse search lag 48: correlation = 0.5965591
🔍 Coarse search lag 51: correlation = 0.52094793
🔍 Coarse search lag 54: correlation = 0.4086685
🔍 Coarse search lag 78: correlation = 0.34334126
🔍 Coarse search lag 81: correlation = 0.49291095
🔍 Coarse search lag 84: correlation = 0.6339471
🔍 Coarse search lag 87: correlation = 0.6796692
🔍 Coarse search lag 90: correlation = 0.5768559
🔍 Coarse search lag 93: correlation = 0.47810888
🔍 Coarse search lag 96: correlation = 0.45841986
🔍 Coarse search lag 99: correlation = 0.3660342
🔍 Coarse search lag 123: correlation = 0.38215104
🔍 Coarse search lag 126: correlation = 0.49779153
🔍 Coarse search lag 129: correlation = 0.57551587
🔍 Coarse search lag 132: correlation = 0.6952798
🔍 Coarse search lag 135: correlation = 0.6467775
🔍 Coarse search lag 138: correlation = 0.5473476
🔍 Fine search around lag 132 in range [127, 137]
🔍 Fine search lag 127: correlation = 0.5229278
🔍 Fine search lag 128: correlation = 0.5432845
🔍 Fine search lag 129: correlation = 0.57551587
🔍 Fine search lag 130: correlation = 0.6236668
🔍 Fine search lag 131: correlation = 0.6651473
🔍 Fine search lag 132: correlation = 0.6952798
🔍 Fine search lag 133: correlation = 0.6978953
🔍 Fine search lag 134: correlation = 0.6715742
🔍 Fine search lag 135: correlation = 0.6467775
🔍 Fine search lag 136: correlation = 0.6146508
🔍 Fine search lag 137: correlation = 0.58318835
🔍 Best correlation: 0.6978953 at period 133 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.0-9.20669 Hz (periods: 106-139)
📊 Moving averages updated - Period: 133.0, Amplitude: 0.74384844, RunLength: 2
✅ Pitch detected: 7.365352 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.365352 Hz
🎵 Detected pitch: 7.365352 Hz (total samples: 369600)
� Calculated pressure: 3.582829 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.365352, Pressure: 3.582829, Breaths: 1
🎤 Audio Level: 0.047453314, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 374400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 3.8090623e-06, Max: 0.0014113629, Avg: 0.00013796202, Variance: 2.8487378e-08
🎯 Economical search: one wavelength ahead 266, derivative adj 0, predicted lag 266, window [240, 139]
⚠️ Invalid economical search range: 240 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=2.7279305e-06, norm1=1.4157877e-05, norm2=1.293724e-05, result=0.20156421
🔍 Coarse search lag 24: correlation = 0.20156421
🔍 Lag 27: corr=2.824879e-06, norm1=1.4123058e-05, norm2=1.29366235e-05, result=0.20898977
🔍 Coarse search lag 27: correlation = 0.20898977
🔍 Lag 30: corr=3.680305e-06, norm1=1.4022148e-05, norm2=1.2897542e-05, result=0.2736674
🔍 Coarse search lag 30: correlation = 0.2736674
🔍 Coarse search lag 33: correlation = 0.3893767
🔍 Coarse search lag 36: correlation = 0.45716104
🔍 Coarse search lag 39: correlation = 0.5428266
🔍 Coarse search lag 42: correlation = 0.62409884
🔍 Coarse search lag 45: correlation = 0.6178969
🔍 Coarse search lag 48: correlation = 0.56390417
🔍 Coarse search lag 51: correlation = 0.48464355
🔍 Coarse search lag 54: correlation = 0.44847754
🔍 Coarse search lag 57: correlation = 0.320534
🔍 Coarse search lag 75: correlation = 0.35242575
🔍 Coarse search lag 78: correlation = 0.4211836
🔍 Coarse search lag 81: correlation = 0.558977
🔍 Coarse search lag 84: correlation = 0.70765436
🔍 Coarse search lag 87: correlation = 0.6798437
🔍 Coarse search lag 90: correlation = 0.6284164
🔍 Coarse search lag 93: correlation = 0.54164237
🔍 Coarse search lag 96: correlation = 0.48404166
🔍 Coarse search lag 99: correlation = 0.35696805
🔍 Coarse search lag 117: correlation = 0.3106096
🔍 Coarse search lag 120: correlation = 0.36812228
🔍 Coarse search lag 123: correlation = 0.48093173
🔍 Coarse search lag 126: correlation = 0.64870673
🔍 Coarse search lag 129: correlation = 0.68737215
🔍 Coarse search lag 132: correlation = 0.6279622
🔍 Coarse search lag 135: correlation = 0.6048368
🔍 Coarse search lag 138: correlation = 0.5751161
🔍 Fine search around lag 84 in range [79, 89]
🔍 Fine search lag 79: correlation = 0.46864063
🔍 Fine search lag 80: correlation = 0.51452535
🔍 Fine search lag 81: correlation = 0.558977
🔍 Fine search lag 82: correlation = 0.62386173
🔍 Fine search lag 83: correlation = 0.67870736
🔍 Fine search lag 84: correlation = 0.70765436
🔍 Fine search lag 85: correlation = 0.7050983
🔍 Fine search lag 86: correlation = 0.6985749
🔍 Fine search lag 87: correlation = 0.6798437
🔍 Fine search lag 88: correlation = 0.66938776
🔍 Fine search lag 89: correlation = 0.6455736
🔍 Best correlation: 0.70765436 at period 84 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 8.746356-14.57726 Hz (periods: 67-112)
🔄 Run length reduced: 2 → 0 (leap: 4.296456 Hz, threshold: 1.4730705 Hz)
🔄 Moving averages reset due to run length = 0
✅ Pitch detected: 11.661808 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 11.661808 Hz
🎵 Detected pitch: 11.661808 Hz (total samples: 374400)
� Calculated pressure: 8.390562 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 11.661808, Pressure: 8.390562, Breaths: 1
🎤 Audio Level: 0.04496882, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 379200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 3.8090623e-06, Max: 0.0007889598, Avg: 0.000118422126, Variance: 1.8160767e-08
🎯 Economical search: one wavelength ahead 168, derivative adj 0, predicted lag 168, window [152, 139]
⚠️ Invalid economical search range: 152 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.8738883e-06, norm1=9.641905e-06, norm2=9.06234e-06, result=0.20046663
🔍 Coarse search lag 24: correlation = 0.20046663
🔍 Lag 27: corr=2.3610116e-06, norm1=9.579857e-06, norm2=9.06127e-06, result=0.2534102
🔍 Coarse search lag 27: correlation = 0.2534102
🔍 Lag 30: corr=3.0164422e-06, norm1=9.549788e-06, norm2=9.060744e-06, result=0.32427704
🔍 Coarse search lag 30: correlation = 0.32427704
🔍 Coarse search lag 33: correlation = 0.44025636
🔍 Coarse search lag 36: correlation = 0.5291549
🔍 Coarse search lag 39: correlation = 0.6193559
🔍 Coarse search lag 42: correlation = 0.69830364
🔍 Coarse search lag 45: correlation = 0.6917973
🔍 Coarse search lag 48: correlation = 0.6279678
🔍 Coarse search lag 51: correlation = 0.54988855
🔍 Coarse search lag 54: correlation = 0.48401242
🔍 Coarse search lag 57: correlation = 0.34054103
🔍 Coarse search lag 75: correlation = 0.32969058
🔍 Coarse search lag 78: correlation = 0.41286367
🔍 Coarse search lag 81: correlation = 0.5278522
🔍 Coarse search lag 84: correlation = 0.6551807
🔍 Coarse search lag 87: correlation = 0.7541506
🔍 Coarse search lag 90: correlation = 0.705896
🔍 Coarse search lag 93: correlation = 0.73896253
🔍 Coarse search lag 96: correlation = 0.64175606
🔍 Coarse search lag 99: correlation = 0.44555146
🔍 Coarse search lag 102: correlation = 0.35809967
🔍 Coarse search lag 120: correlation = 0.35249174
🔍 Coarse search lag 123: correlation = 0.4476109
🔍 Coarse search lag 126: correlation = 0.579665
🔍 Coarse search lag 129: correlation = 0.6774626
🔍 Coarse search lag 132: correlation = 0.64029
🔍 Coarse search lag 135: correlation = 0.7126749
🔍 Coarse search lag 138: correlation = 0.67622405
🔍 Fine search around lag 87 in range [82, 92]
🔍 Fine search lag 82: correlation = 0.56253284
🔍 Fine search lag 83: correlation = 0.61165667
🔍 Fine search lag 84: correlation = 0.6551807
🔍 Fine search lag 85: correlation = 0.690075
🔍 Fine search lag 86: correlation = 0.729198
🔍 Fine search lag 87: correlation = 0.7541506
🔍 Fine search lag 88: correlation = 0.7528885
🔍 Fine search lag 89: correlation = 0.7316045
🔍 Fine search lag 90: correlation = 0.705896
🔍 Fine search lag 91: correlation = 0.70589393
🔍 Fine search lag 92: correlation = 0.7203563
🔍 Best correlation: 0.7541506 at period 87 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 8.444757-14.074596 Hz (periods: 69-116)
📊 Moving averages updated - Period: 87.0, Amplitude: 0.7541506, RunLength: 1
✅ Pitch detected: 11.259677 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 11.259677 Hz
🎵 Detected pitch: 11.259677 Hz (total samples: 379200)
� Calculated pressure: 7.940578 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 11.259677, Pressure: 7.940578, Breaths: 1
🎤 Audio Level: 0.06848416, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 384000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 3.8090623e-06, Max: 0.0009503585, Avg: 0.00011315139, Variance: 2.0077916e-08
🎯 Economical search: one wavelength ahead 174, derivative adj 0, predicted lag 174, window [157, 139]
⚠️ Invalid economical search range: 157 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.6734717e-06, norm1=9.441408e-06, norm2=9.819933e-06, result=0.17379838
🔍 Coarse search lag 24: correlation = 0.17379838
🔍 Lag 27: corr=2.0159255e-06, norm1=9.429223e-06, norm2=9.779461e-06, result=0.20993221
🔍 Coarse search lag 27: correlation = 0.20993221
🔍 Lag 30: corr=2.4269032e-06, norm1=9.392793e-06, norm2=9.6006315e-06, result=0.25556725
🔍 Coarse search lag 30: correlation = 0.25556725
🔍 Coarse search lag 33: correlation = 0.33377558
🔍 Coarse search lag 36: correlation = 0.4418748
🔍 Coarse search lag 39: correlation = 0.57031816
🔍 Coarse search lag 42: correlation = 0.5989558
🔍 Coarse search lag 45: correlation = 0.5978163
🔍 Coarse search lag 48: correlation = 0.542638
🔍 Coarse search lag 51: correlation = 0.5208869
🔍 Coarse search lag 54: correlation = 0.43396598
🔍 Coarse search lag 57: correlation = 0.34161702
🔍 Coarse search lag 75: correlation = 0.32056248
🔍 Coarse search lag 78: correlation = 0.39093477
🔍 Coarse search lag 81: correlation = 0.44693184
🔍 Coarse search lag 84: correlation = 0.60723454
🔍 Coarse search lag 87: correlation = 0.65759504
🔍 Coarse search lag 90: correlation = 0.69260097
🔍 Coarse search lag 93: correlation = 0.7796689
🔍 Coarse search lag 96: correlation = 0.61313254
🔍 Coarse search lag 99: correlation = 0.45719573
🔍 Coarse search lag 102: correlation = 0.3724708
🔍 Coarse search lag 123: correlation = 0.36430418
🔍 Coarse search lag 126: correlation = 0.48921466
🔍 Coarse search lag 129: correlation = 0.65463555
🔍 Coarse search lag 132: correlation = 0.641358
🔍 Coarse search lag 135: correlation = 0.6196999
🔍 Coarse search lag 138: correlation = 0.5820406
🔍 Fine search around lag 93 in range [88, 98]
🔍 Fine search lag 88: correlation = 0.6486332
🔍 Fine search lag 89: correlation = 0.65840083
🔍 Fine search lag 90: correlation = 0.69260097
🔍 Fine search lag 91: correlation = 0.7484813
🔍 Fine search lag 92: correlation = 0.7736473
🔍 Fine search lag 93: correlation = 0.7796689
🔍 Fine search lag 94: correlation = 0.7559465
🔍 Fine search lag 95: correlation = 0.69540584
🔍 Fine search lag 96: correlation = 0.61313254
🔍 Fine search lag 97: correlation = 0.5375786
🔍 Fine search lag 98: correlation = 0.49116808
🔍 Best correlation: 0.7796689 at period 93 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.899935-13.166557 Hz (periods: 74-123)
📊 Moving averages updated - Period: 90.0, Amplitude: 0.7669098, RunLength: 2
✅ Pitch detected: 10.533246 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 10.533246 Hz
🎵 Detected pitch: 10.533246 Hz (total samples: 384000)
� Calculated pressure: 7.127702 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 10.533246, Pressure: 7.127702, Breaths: 1
🎤 Audio Level: 0.040881522, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 388800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 4.4504977e-06, Max: 0.0009503585, Avg: 0.00010579202, Variance: 1.5967132e-08
🎯 Economical search: one wavelength ahead 186, derivative adj 3, predicted lag 189, window [171, 139]
⚠️ Invalid economical search range: 171 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.4530569e-06, norm1=7.2998023e-06, norm2=7.926256e-06, result=0.19102624
🔍 Coarse search lag 24: correlation = 0.19102624
🔍 Lag 27: corr=1.659226e-06, norm1=7.2933994e-06, norm2=7.545778e-06, result=0.22366011
🔍 Coarse search lag 27: correlation = 0.22366011
🔍 Lag 30: corr=1.921559e-06, norm1=7.285074e-06, norm2=7.3537735e-06, result=0.2625316
🔍 Coarse search lag 30: correlation = 0.2625316
🔍 Coarse search lag 33: correlation = 0.36188284
🔍 Coarse search lag 36: correlation = 0.49646637
🔍 Coarse search lag 39: correlation = 0.59881675
🔍 Coarse search lag 42: correlation = 0.62415785
🔍 Coarse search lag 45: correlation = 0.625846
🔍 Coarse search lag 48: correlation = 0.5861168
🔍 Coarse search lag 51: correlation = 0.5631905
🔍 Coarse search lag 54: correlation = 0.4059087
🔍 Coarse search lag 57: correlation = 0.31978485
🔍 Coarse search lag 78: correlation = 0.3584694
🔍 Coarse search lag 81: correlation = 0.4079916
🔍 Coarse search lag 84: correlation = 0.5689535
🔍 Coarse search lag 87: correlation = 0.59633183
🔍 Coarse search lag 90: correlation = 0.61612815
🔍 Coarse search lag 93: correlation = 0.6611416
🔍 Coarse search lag 96: correlation = 0.56294036
🔍 Coarse search lag 99: correlation = 0.42231435
🔍 Coarse search lag 102: correlation = 0.3270828
🔍 Coarse search lag 123: correlation = 0.3362868
🔍 Coarse search lag 126: correlation = 0.4510367
🔍 Coarse search lag 129: correlation = 0.63913107
🔍 Coarse search lag 132: correlation = 0.69799435
🔍 Coarse search lag 135: correlation = 0.7489694
🔍 Coarse search lag 138: correlation = 0.70800966
🔍 Fine search around lag 135 in range [130, 139]
🔍 Fine search lag 130: correlation = 0.63778454
🔍 Fine search lag 131: correlation = 0.65288913
🔍 Fine search lag 132: correlation = 0.69799435
🔍 Fine search lag 133: correlation = 0.7396535
🔍 Fine search lag 134: correlation = 0.73828715
🔍 Fine search lag 135: correlation = 0.7489694
🔍 Fine search lag 136: correlation = 0.7425548
🔍 Fine search lag 137: correlation = 0.73160785
🔍 Fine search lag 138: correlation = 0.70800966
🔍 Fine search lag 139: correlation = 0.6600184
🔍 Best correlation: 0.7489694 at period 135 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.0-9.070295 Hz (periods: 107-139)
🔄 Run length reduced: 2 → 1 (leap: 3.6281176 Hz, threshold: 2.1768708 Hz)
📊 Moving averages updated - Period: 112.5, Amplitude: 0.7579396, RunLength: 2
✅ Pitch detected: 7.256236 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.256236 Hz
🎵 Detected pitch: 7.256236 Hz (total samples: 388800)
� Calculated pressure: 3.4607282 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.256236, Pressure: 3.4607282, Breaths: 1
🎤 Audio Level: 0.067312725, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 393600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 1.9024278e-06, Max: 0.0015598717, Avg: 0.00011657403, Variance: 2.7065052e-08
🎯 Economical search: one wavelength ahead 270, derivative adj 24, predicted lag 294, window [267, 139]
⚠️ Invalid economical search range: 267 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.2670046e-06, norm1=7.2676557e-06, norm2=1.1340529e-05, result=0.13956109
🔍 Coarse search lag 24: correlation = 0.13956109
🔍 Lag 27: corr=1.4876415e-06, norm1=7.267236e-06, norm2=1.1273902e-05, result=0.16435258
🔍 Coarse search lag 27: correlation = 0.16435258
🔍 Lag 30: corr=1.6801234e-06, norm1=7.2670286e-06, norm2=1.1066459e-05, result=0.18735203
🔍 Coarse search lag 30: correlation = 0.18735203
🔍 Coarse search lag 36: correlation = 0.39674202
🔍 Coarse search lag 39: correlation = 0.53915995
🔍 Coarse search lag 42: correlation = 0.5922804
🔍 Coarse search lag 45: correlation = 0.5714507
🔍 Coarse search lag 48: correlation = 0.5881961
🔍 Coarse search lag 51: correlation = 0.5288524
🔍 Coarse search lag 54: correlation = 0.38173282
🔍 Coarse search lag 81: correlation = 0.38392216
🔍 Coarse search lag 84: correlation = 0.5047559
🔍 Coarse search lag 87: correlation = 0.53189313
🔍 Coarse search lag 90: correlation = 0.6523417
🔍 Coarse search lag 93: correlation = 0.6390331
🔍 Coarse search lag 96: correlation = 0.6367905
🔍 Coarse search lag 99: correlation = 0.45577034
🔍 Coarse search lag 102: correlation = 0.35332945
🔍 Coarse search lag 129: correlation = 0.38762116
🔍 Coarse search lag 132: correlation = 0.5260323
🔍 Coarse search lag 135: correlation = 0.5862525
🔍 Coarse search lag 138: correlation = 0.6179452
🔍 Fine search around lag 90 in range [85, 95]
🔍 Fine search lag 85: correlation = 0.503711
🔍 Fine search lag 86: correlation = 0.5098426
🔍 Fine search lag 87: correlation = 0.53189313
🔍 Fine search lag 88: correlation = 0.57131416
🔍 Fine search lag 89: correlation = 0.62644875
🔍 Fine search lag 90: correlation = 0.6523417
🔍 Fine search lag 91: correlation = 0.66314095
🔍 Fine search lag 92: correlation = 0.63603616
🔍 Fine search lag 93: correlation = 0.6390331
🔍 Fine search lag 94: correlation = 0.63931173
🔍 Fine search lag 95: correlation = 0.6370357
🔍 Best correlation: 0.66314095 at period 91 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 8.073559-13.455933 Hz (periods: 72-121)
🔄 Run length reduced: 2 → 1 (leap: 2.0572624 Hz, threshold: 1.7414967 Hz)
📊 Moving averages updated - Period: 101.75, Amplitude: 0.7105403, RunLength: 2
✅ Pitch detected: 10.764746 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 10.764746 Hz
🎵 Detected pitch: 10.764746 Hz (total samples: 393600)
� Calculated pressure: 7.3867497 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 10.764746, Pressure: 7.3867497, Breaths: 1
🎤 Audio Level: 0.041928653, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 398400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 9.953361e-07, Max: 0.0015598717, Avg: 0.00011188378, Variance: 2.4297941e-08
🎯 Economical search: one wavelength ahead 182, derivative adj 1, predicted lag 183, window [165, 139]
⚠️ Invalid economical search range: 165 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=9.94572e-07, norm1=9.7820985e-06, norm2=1.04232495e-05, result=0.09849601
🔍 Coarse search lag 24: correlation = 0.09849601
🔍 Lag 27: corr=1.1947354e-06, norm1=9.782028e-06, norm2=1.0404361e-05, result=0.11842669
🔍 Coarse search lag 27: correlation = 0.11842669
🔍 Lag 30: corr=1.4374324e-06, norm1=9.781789e-06, norm2=1.0397893e-05, result=0.14252977
🔍 Coarse search lag 30: correlation = 0.14252977
🔍 Coarse search lag 39: correlation = 0.44364932
🔍 Coarse search lag 42: correlation = 0.5520528
🔍 Coarse search lag 45: correlation = 0.57371503
🔍 Coarse search lag 48: correlation = 0.5825427
🔍 Coarse search lag 51: correlation = 0.5485577
🔍 Coarse search lag 54: correlation = 0.41421682
🔍 Coarse search lag 57: correlation = 0.32493523
🔍 Coarse search lag 81: correlation = 0.30680266
🔍 Coarse search lag 84: correlation = 0.37280986
🔍 Coarse search lag 87: correlation = 0.41826254
🔍 Coarse search lag 90: correlation = 0.5728732
🔍 Coarse search lag 93: correlation = 0.6424118
🔍 Coarse search lag 96: correlation = 0.68556017
🔍 Coarse search lag 99: correlation = 0.4986044
🔍 Coarse search lag 102: correlation = 0.37978414
🔍 Coarse search lag 129: correlation = 0.38791692
🔍 Coarse search lag 132: correlation = 0.52862
🔍 Coarse search lag 135: correlation = 0.57891
🔍 Coarse search lag 138: correlation = 0.6247765
🔍 Fine search around lag 96 in range [91, 101]
🔍 Fine search lag 91: correlation = 0.59737355
🔍 Fine search lag 92: correlation = 0.6111339
🔍 Fine search lag 93: correlation = 0.6424118
🔍 Fine search lag 94: correlation = 0.65855455
🔍 Fine search lag 95: correlation = 0.67981243
🔍 Fine search lag 96: correlation = 0.68556017
🔍 Fine search lag 97: correlation = 0.6283931
🔍 Fine search lag 98: correlation = 0.5579217
🔍 Fine search lag 99: correlation = 0.4986044
🔍 Fine search lag 100: correlation = 0.45588568
🔍 Fine search lag 101: correlation = 0.42035532
🔍 Best correlation: 0.68556017 at period 96 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.653061-12.755102 Hz (periods: 76-128)
📊 Moving averages updated - Period: 99.83333, Amplitude: 0.7022136, RunLength: 3
✅ Pitch detected: 10.204082 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 10.204082 Hz
🎵 Detected pitch: 10.204082 Hz (total samples: 398400)
� Calculated pressure: 6.7593665 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 10.204082, Pressure: 6.7593665, Breaths: 1
🎤 Audio Level: 0.05001284, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 403200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 9.953361e-07, Max: 0.0015598717, Avg: 0.00012199655, Variance: 2.8022745e-08
🎯 Economical search: one wavelength ahead 192, derivative adj 0, predicted lag 192, window [173, 139]
⚠️ Invalid economical search range: 173 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=7.5450646e-07, norm1=1.2336514e-05, norm2=1.1332034e-05, result=0.06381353
🔍 Coarse search lag 24: correlation = 0.06381353
🔍 Lag 27: corr=1.0726058e-06, norm1=1.2332521e-05, norm2=1.1329979e-05, result=0.09074018
🔍 Coarse search lag 27: correlation = 0.09074018
🔍 Lag 30: corr=1.5556006e-06, norm1=1.2332401e-05, norm2=1.1327913e-05, result=0.13161317
🔍 Coarse search lag 30: correlation = 0.13161317
🔍 Coarse search lag 39: correlation = 0.4719551
🔍 Coarse search lag 42: correlation = 0.6000518
🔍 Coarse search lag 45: correlation = 0.58378243
🔍 Coarse search lag 48: correlation = 0.5712886
🔍 Coarse search lag 51: correlation = 0.5463111
🔍 Coarse search lag 54: correlation = 0.4160533
🔍 Coarse search lag 57: correlation = 0.31238386
🔍 Coarse search lag 84: correlation = 0.38994807
🔍 Coarse search lag 87: correlation = 0.4524809
🔍 Coarse search lag 90: correlation = 0.58002025
🔍 Coarse search lag 93: correlation = 0.6416267
🔍 Coarse search lag 96: correlation = 0.688493
🔍 Coarse search lag 99: correlation = 0.5185671
🔍 Coarse search lag 102: correlation = 0.42602685
🔍 Coarse search lag 105: correlation = 0.31686908
🔍 Coarse search lag 132: correlation = 0.43815675
🔍 Coarse search lag 135: correlation = 0.6493216
🔍 Coarse search lag 138: correlation = 0.79988647
🔍 Fine search around lag 138 in range [133, 139]
🔍 Fine search lag 133: correlation = 0.5003511
🔍 Fine search lag 134: correlation = 0.5734496
🔍 Fine search lag 135: correlation = 0.6493216
🔍 Fine search lag 136: correlation = 0.70796824
🔍 Fine search lag 137: correlation = 0.78567886
🔍 Fine search lag 138: correlation = 0.79988647
🔍 Fine search lag 139: correlation = 0.7916271
🔍 Best correlation: 0.79988647 at period 138 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.0-8.873115 Hz (periods: 110-139)
🔄 Run length reduced: 3 → 2 (leap: 2.7137814 Hz, threshold: 1.9624547 Hz)
📊 Moving averages updated - Period: 112.55556, Amplitude: 0.73477125, RunLength: 3
✅ Pitch detected: 7.0984917 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.0984917 Hz
🎵 Detected pitch: 7.0984917 Hz (total samples: 403200)
� Calculated pressure: 3.284212 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.0984917, Pressure: 3.284212, Breaths: 1
🎤 Audio Level: 0.036756046, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 408000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 9.953361e-07, Max: 0.0015598717, Avg: 0.000105266845, Variance: 2.4947745e-08
🎯 Economical search: one wavelength ahead 276, derivative adj 8, predicted lag 284, window [257, 139]
⚠️ Invalid economical search range: 257 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=4.897163e-07, norm1=1.0523382e-05, norm2=6.5178806e-06, result=0.059130788
🔍 Coarse search lag 24: correlation = 0.059130788
🔍 Lag 27: corr=7.5725194e-07, norm1=1.0516659e-05, norm2=6.5178606e-06, result=0.09146374
🔍 Coarse search lag 27: correlation = 0.09146374
🔍 Lag 30: corr=1.2040928e-06, norm1=1.0515549e-05, norm2=6.5178424e-06, result=0.14544275
🔍 Coarse search lag 30: correlation = 0.14544275
🔍 Coarse search lag 36: correlation = 0.3258844
🔍 Coarse search lag 39: correlation = 0.5291174
🔍 Coarse search lag 42: correlation = 0.6496234
🔍 Coarse search lag 45: correlation = 0.63077956
🔍 Coarse search lag 48: correlation = 0.6303114
🔍 Coarse search lag 51: correlation = 0.6606554
🔍 Coarse search lag 54: correlation = 0.4957724
🔍 Coarse search lag 57: correlation = 0.3650828
🔍 Coarse search lag 84: correlation = 0.3690616
🔍 Coarse search lag 87: correlation = 0.49179247
🔍 Coarse search lag 90: correlation = 0.59176457
🔍 Coarse search lag 93: correlation = 0.69073945
🔍 Coarse search lag 96: correlation = 0.7176282
🔍 Coarse search lag 99: correlation = 0.5891819
🔍 Coarse search lag 102: correlation = 0.39613703
🔍 Coarse search lag 105: correlation = 0.31199697
🔍 Coarse search lag 132: correlation = 0.44203568
🔍 Coarse search lag 135: correlation = 0.64406997
🔍 Coarse search lag 138: correlation = 0.7774093
🔍 Fine search around lag 138 in range [133, 139]
🔍 Fine search lag 133: correlation = 0.508852
🔍 Fine search lag 134: correlation = 0.58266485
🔍 Fine search lag 135: correlation = 0.64406997
🔍 Fine search lag 136: correlation = 0.67755896
🔍 Fine search lag 137: correlation = 0.7493157
🔍 Fine search lag 138: correlation = 0.7774093
🔍 Fine search lag 139: correlation = 0.77225477
🔍 Best correlation: 0.7774093 at period 138 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.0-8.873115 Hz (periods: 110-139)
📊 Moving averages updated - Period: 118.91667, Amplitude: 0.74543077, RunLength: 4
✅ Pitch detected: 7.0984917 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 7.0984917 Hz
🎵 Detected pitch: 7.0984917 Hz (total samples: 408000)
� Calculated pressure: 3.284212 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 7.0984917, Pressure: 3.284212, Breaths: 1
🎤 Audio Level: 0.034135625, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 412800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 7.698677e-07, Max: 0.00066859944, Avg: 8.601934e-05, Variance: 1.23977015e-08
🎯 Economical search: one wavelength ahead 276, derivative adj 9, predicted lag 285, window [258, 139]
⚠️ Invalid economical search range: 258 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=3.7683247e-07, norm1=5.3688295e-06, norm2=5.035184e-06, result=0.07247711
🔍 Coarse search lag 24: correlation = 0.07247711
🔍 Lag 27: corr=5.5818214e-07, norm1=5.3675412e-06, norm2=5.035047e-06, result=0.107370876
🔍 Coarse search lag 27: correlation = 0.107370876
🔍 Lag 30: corr=9.784465e-07, norm1=5.3674953e-06, norm2=5.0350322e-06, result=0.18821321
🔍 Coarse search lag 30: correlation = 0.18821321
🔍 Coarse search lag 36: correlation = 0.3962079
🔍 Coarse search lag 39: correlation = 0.57825404
🔍 Coarse search lag 42: correlation = 0.6913198
🔍 Coarse search lag 45: correlation = 0.6689605
🔍 Coarse search lag 48: correlation = 0.73327523
🔍 Coarse search lag 51: correlation = 0.7394434
🔍 Coarse search lag 54: correlation = 0.6170347
🔍 Coarse search lag 57: correlation = 0.4585733
🔍 Coarse search lag 60: correlation = 0.33873278
🔍 Coarse search lag 81: correlation = 0.38854772
🔍 Coarse search lag 84: correlation = 0.46154186
🔍 Coarse search lag 87: correlation = 0.5883866
🔍 Coarse search lag 90: correlation = 0.73458236
🔍 Coarse search lag 93: correlation = 0.75720537
🔍 Coarse search lag 96: correlation = 0.67190254
🔍 Coarse search lag 99: correlation = 0.62555003
🔍 Coarse search lag 102: correlation = 0.5212233
🔍 Coarse search lag 105: correlation = 0.41178545
🔍 Coarse search lag 108: correlation = 0.31534278
🔍 Coarse search lag 129: correlation = 0.37654907
🔍 Coarse search lag 132: correlation = 0.503035
🔍 Coarse search lag 135: correlation = 0.5732605
🔍 Coarse search lag 138: correlation = 0.6068122
🔍 Fine search around lag 93 in range [88, 98]
🔍 Fine search lag 88: correlation = 0.6561787
🔍 Fine search lag 89: correlation = 0.69055545
🔍 Fine search lag 90: correlation = 0.73458236
🔍 Fine search lag 91: correlation = 0.73328525
🔍 Fine search lag 92: correlation = 0.7523505
🔍 Fine search lag 93: correlation = 0.75720537
🔍 Fine search lag 94: correlation = 0.7271679
🔍 Fine search lag 95: correlation = 0.6954846
🔍 Fine search lag 96: correlation = 0.67190254
🔍 Fine search lag 97: correlation = 0.6615522
🔍 Fine search lag 98: correlation = 0.64607596
🔍 Best correlation: 0.75720537 at period 93 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.899935-13.166557 Hz (periods: 74-123)
🔄 Run length reduced: 4 → 3 (leap: 2.2956133 Hz, threshold: 1.6475266 Hz)
📊 Moving averages updated - Period: 112.4375, Amplitude: 0.74837446, RunLength: 4
✅ Pitch detected: 10.533246 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 10.533246 Hz
🎵 Detected pitch: 10.533246 Hz (total samples: 412800)
� Calculated pressure: 7.127702 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 10.533246, Pressure: 7.127702, Breaths: 1
🎤 Audio Level: 0.033307455, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 417600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 7.1126914e-07, Max: 0.00043268024, Avg: 5.816143e-05, Variance: 5.1141016e-09
🎯 Economical search: one wavelength ahead 186, derivative adj 3, predicted lag 189, window [171, 139]
⚠️ Invalid economical search range: 171 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=2.1603458e-07, norm1=2.3830992e-06, norm2=2.4635856e-06, result=0.08915965
🔍 Coarse search lag 24: correlation = 0.08915965
🔍 Lag 27: corr=2.5907798e-07, norm1=2.3830744e-06, norm2=2.4635685e-06, result=0.106925026
🔍 Coarse search lag 27: correlation = 0.106925026
🔍 Lag 30: corr=3.9632582e-07, norm1=2.3830646e-06, norm2=2.4629576e-06, result=0.16358972
🔍 Coarse search lag 30: correlation = 0.16358972
🔍 Coarse search lag 36: correlation = 0.356135
🔍 Coarse search lag 39: correlation = 0.46059784
🔍 Coarse search lag 42: correlation = 0.5378059
🔍 Coarse search lag 45: correlation = 0.63807297
🔍 Coarse search lag 48: correlation = 0.71286225
🔍 Coarse search lag 51: correlation = 0.71154755
🔍 Coarse search lag 54: correlation = 0.6619446
🔍 Coarse search lag 57: correlation = 0.6074266
🔍 Coarse search lag 60: correlation = 0.49727413
🔍 Coarse search lag 63: correlation = 0.35632336
🔍 Coarse search lag 87: correlation = 0.35148436
🔍 Coarse search lag 90: correlation = 0.46350747
🔍 Coarse search lag 93: correlation = 0.5915146
🔍 Coarse search lag 96: correlation = 0.6306827
🔍 Coarse search lag 99: correlation = 0.6514676
🔍 Coarse search lag 102: correlation = 0.622506
🔍 Coarse search lag 105: correlation = 0.6456181
🔍 Coarse search lag 108: correlation = 0.6157586
🔍 Coarse search lag 111: correlation = 0.5442536
🔍 Coarse search lag 114: correlation = 0.43775937
🔍 Coarse search lag 138: correlation = 0.31528702
🔍 Fine search around lag 48 in range [43, 53]
🔍 Fine search lag 43: correlation = 0.5664745
🔍 Fine search lag 44: correlation = 0.6021763
🔍 Fine search lag 45: correlation = 0.63807297
🔍 Fine search lag 46: correlation = 0.67444104
🔍 Fine search lag 47: correlation = 0.70633096
🔍 Fine search lag 48: correlation = 0.71286225
🔍 Fine search lag 49: correlation = 0.7090689
🔍 Fine search lag 50: correlation = 0.713924
🔍 Fine search lag 51: correlation = 0.71154755
🔍 Fine search lag 52: correlation = 0.68981785
🔍 Fine search lag 53: correlation = 0.68735605
🔍 Best correlation: 0.713924 at period 50 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 14.693878-24.489796 Hz (periods: 40-66)
🔄 Run length reduced: 4 → 0 (leap: 10.879514 Hz, threshold: 1.7424647 Hz)
🔄 Moving averages reset due to run length = 0
✅ Pitch detected: 19.591837 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 19.591837 Hz
🎵 Detected pitch: 19.591837 Hz (total samples: 417600)
� Calculated pressure: 17.264265 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🫁 Breath detection: Breath now active (duration: 1.6s)
🖥️ UI updated - Freq: 19.591837, Pressure: 17.264265, Breaths: 1
🎤 Audio Level: 0.029733006, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 422400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 2.678641e-07, Max: 0.00032259733, Avg: 4.7382957e-05, Variance: 4.089549e-09
🎯 Economical search: one wavelength ahead 100, derivative adj 0, predicted lag 100, window [90, 110]
🎯 Economical lag 93: correlation = 0.30734345
🎯 Economical lag 94: correlation = 0.33519924
🎯 Economical lag 95: correlation = 0.3620974
🎯 Economical lag 96: correlation = 0.3905483
🎯 Economical lag 97: correlation = 0.4273927
🎯 Economical lag 98: correlation = 0.4457963
🎯 Economical lag 99: correlation = 0.49099413
🎯 Economical lag 100: correlation = 0.5309201
🎯 Economical lag 101: correlation = 0.57860094
🎯 Economical lag 102: correlation = 0.61248386
🎯 Economical lag 103: correlation = 0.684139
🎯 Economical lag 104: correlation = 0.70079225
🎯 Economical lag 105: correlation = 0.71186125
🎯 Economical lag 106: correlation = 0.71169335
🎯 Economical lag 107: correlation = 0.6918896
🎯 Economical lag 108: correlation = 0.65667415
🎯 Economical lag 109: correlation = 0.6283185
🎯 Economical lag 110: correlation = 0.6009401
🔍 Best correlation: 0.71186125 at period 105 (threshold: 0.6) [strategy: economical]
🎯 Target range updated: 7.0-11.661807 Hz (periods: 84-139)
📊 Moving averages updated - Period: 105.0, Amplitude: 0.71186125, RunLength: 1
✅ Pitch detected: 9.329446 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 9.329446 Hz
🎵 Detected pitch: 9.329446 Hz (total samples: 422400)
� Calculated pressure: 5.7806497 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 9.329446, Pressure: 5.7806497, Breaths: 1
🎤 Audio Level: 0.027774267, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 427200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 2.50083e-07, Max: 0.00032259733, Avg: 4.0907293e-05, Variance: 3.6248564e-09
🎯 Economical search: one wavelength ahead 210, derivative adj 0, predicted lag 210, window [189, 139]
⚠️ Invalid economical search range: 189 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=1.233605e-07, norm1=1.5720095e-06, norm2=1.2471556e-06, result=0.08810249
🔍 Coarse search lag 24: correlation = 0.08810249
🔍 Lag 27: corr=1.1319085e-07, norm1=1.5720076e-06, norm2=1.2471501e-06, result=0.08083969
🔍 Coarse search lag 27: correlation = 0.08083969
🔍 Lag 30: corr=1.14334306e-07, norm1=1.5720069e-06, norm2=1.2471281e-06, result=0.081657074
🔍 Coarse search lag 30: correlation = 0.081657074
🔍 Coarse search lag 42: correlation = 0.3485382
🔍 Coarse search lag 45: correlation = 0.4252284
🔍 Coarse search lag 48: correlation = 0.48962015
🔍 Coarse search lag 51: correlation = 0.511601
🔍 Coarse search lag 54: correlation = 0.5416041
🔍 Coarse search lag 57: correlation = 0.5571079
🔍 Coarse search lag 60: correlation = 0.5235834
🔍 Coarse search lag 63: correlation = 0.5133736
🔍 Coarse search lag 66: correlation = 0.44180873
🔍 Coarse search lag 69: correlation = 0.35414472
🔍 Coarse search lag 99: correlation = 0.3583935
🔍 Coarse search lag 102: correlation = 0.49514005
🔍 Coarse search lag 105: correlation = 0.5288421
🔍 Coarse search lag 108: correlation = 0.53378123
🔍 Coarse search lag 111: correlation = 0.5638099
🔍 Coarse search lag 114: correlation = 0.6269166
🔍 Coarse search lag 117: correlation = 0.7130743
🔍 Coarse search lag 120: correlation = 0.5808619
🔍 Coarse search lag 123: correlation = 0.56566626
🔍 Coarse search lag 126: correlation = 0.54324484
🔍 Coarse search lag 129: correlation = 0.39089626
🔍 Coarse search lag 132: correlation = 0.356166
🔍 Coarse search lag 135: correlation = 0.30250543
🔍 Fine search around lag 117 in range [112, 122]
🔍 Fine search lag 112: correlation = 0.5915116
🔍 Fine search lag 113: correlation = 0.616841
🔍 Fine search lag 114: correlation = 0.6269166
🔍 Fine search lag 115: correlation = 0.66508496
🔍 Fine search lag 116: correlation = 0.67379606
🔍 Fine search lag 117: correlation = 0.7130743
🔍 Fine search lag 118: correlation = 0.6909564
🔍 Fine search lag 119: correlation = 0.6247413
🔍 Fine search lag 120: correlation = 0.5808619
🔍 Fine search lag 121: correlation = 0.535041
🔍 Fine search lag 122: correlation = 0.53597903
🔍 Best correlation: 0.7130743 at period 117 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.0-10.465725 Hz (periods: 93-139)
📊 Moving averages updated - Period: 111.0, Amplitude: 0.7124678, RunLength: 2
✅ Pitch detected: 8.37258 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 8.37258 Hz
🎵 Detected pitch: 8.37258 Hz (total samples: 427200)
� Calculated pressure: 4.7099166 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 8.37258, Pressure: 4.7099166, Breaths: 1
🎤 Audio Level: 0.010872388, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 432000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 3.645967e-08, Max: 0.00028084917, Avg: 2.6883086e-05, Variance: 2.1424325e-09
🎯 Economical search: one wavelength ahead 234, derivative adj 6, predicted lag 240, window [217, 139]
⚠️ Invalid economical search range: 217 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=9.312699e-08, norm1=8.5953894e-07, norm2=3.8813323e-07, result=0.16123243
🔍 Coarse search lag 24: correlation = 0.16123243
🔍 Lag 27: corr=8.0243794e-08, norm1=8.5953894e-07, norm2=3.8730633e-07, result=0.13907573
🔍 Coarse search lag 27: correlation = 0.13907573
🔍 Lag 30: corr=6.336211e-08, norm1=8.595383e-07, norm2=3.869738e-07, result=0.10986421
🔍 Coarse search lag 30: correlation = 0.10986421
🔍 Coarse search lag 45: correlation = 0.30862528
🔍 Coarse search lag 48: correlation = 0.36335102
🔍 Coarse search lag 51: correlation = 0.3825743
🔍 Coarse search lag 54: correlation = 0.42373505
🔍 Coarse search lag 57: correlation = 0.43845809
🔍 Coarse search lag 60: correlation = 0.4335362
🔍 Coarse search lag 63: correlation = 0.46298528
🔍 Coarse search lag 66: correlation = 0.46040788
🔍 Coarse search lag 69: correlation = 0.46685243
🔍 Coarse search lag 72: correlation = 0.4480728
🔍 Coarse search lag 75: correlation = 0.3404996
🔍 Coarse search lag 105: correlation = 0.34503672
🔍 Coarse search lag 108: correlation = 0.45306933
🔍 Coarse search lag 111: correlation = 0.5087369
🔍 Coarse search lag 114: correlation = 0.59227043
🔍 Coarse search lag 117: correlation = 0.6886376
🔍 Coarse search lag 120: correlation = 0.5731368
🔍 Coarse search lag 123: correlation = 0.5615423
🔍 Coarse search lag 126: correlation = 0.5805386
🔍 Coarse search lag 129: correlation = 0.43166488
🔍 Coarse search lag 132: correlation = 0.56102264
🔍 Coarse search lag 135: correlation = 0.59052896
🔍 Coarse search lag 138: correlation = 0.5157424
🔍 Fine search around lag 117 in range [112, 122]
🔍 Fine search lag 112: correlation = 0.5433607
🔍 Fine search lag 113: correlation = 0.56468654
🔍 Fine search lag 114: correlation = 0.59227043
🔍 Fine search lag 115: correlation = 0.6480076
🔍 Fine search lag 116: correlation = 0.65235996
🔍 Fine search lag 117: correlation = 0.6886376
🔍 Fine search lag 118: correlation = 0.6910225
🔍 Fine search lag 119: correlation = 0.6235867
🔍 Fine search lag 120: correlation = 0.5731368
🔍 Fine search lag 121: correlation = 0.5123207
🔍 Fine search lag 122: correlation = 0.52358794
🔍 Best correlation: 0.6910225 at period 118 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 7.0-10.377033 Hz (periods: 94-139)
📊 Moving averages updated - Period: 113.33333, Amplitude: 0.7053194, RunLength: 3
✅ Pitch detected: 8.301626 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 8.301626 Hz
🎵 Detected pitch: 8.301626 Hz (total samples: 432000)
� Calculated pressure: 4.6305194 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 8.301626, Pressure: 4.6305194, Breaths: 1
🎤 Audio Level: 0.0012050819, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 436800
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 2.935782e-09, Max: 0.00026984196, Avg: 1.2291649e-05, Variance: 8.946402e-10
🎯 Economical search: one wavelength ahead 236, derivative adj 5, predicted lag 241, window [218, 139]
⚠️ Invalid economical search range: 218 > 139, skipping economical search
🔍 Performing two-stage autocorrelation search
🔍 Lag 24: corr=5.8550466e-08, norm1=3.137172e-07, norm2=2.8100547e-07, result=0.1971986
🔍 Coarse search lag 24: correlation = 0.1971986
🔍 Lag 27: corr=5.0732872e-08, norm1=3.137172e-07, norm2=2.402652e-07, result=0.18478847
🔍 Coarse search lag 27: correlation = 0.18478847
🔍 Lag 30: corr=3.8710095e-08, norm1=3.137172e-07, norm2=2.1027677e-07, result=0.15071602
🔍 Coarse search lag 30: correlation = 0.15071602
🔍 Coarse search lag 63: correlation = 0.40082458
🔍 Coarse search lag 66: correlation = 0.42898473
🔍 Coarse search lag 69: correlation = 0.5621134
🔍 Coarse search lag 72: correlation = 0.7271098
🔍 Coarse search lag 75: correlation = 0.6028435
🔍 Coarse search lag 78: correlation = 0.6242884
🔍 Coarse search lag 81: correlation = 0.5454193
🔍 Coarse search lag 84: correlation = 0.46165875
🔍 Coarse search lag 87: correlation = 0.36367443
🔍 Coarse search lag 90: correlation = 0.3536948
🔍 Coarse search lag 93: correlation = 0.3433864
🔍 Coarse search lag 96: correlation = 0.40900248
🔍 Coarse search lag 99: correlation = 0.4712211
🔍 Coarse search lag 102: correlation = 0.48388866
🔍 Coarse search lag 105: correlation = 0.48025972
🔍 Coarse search lag 108: correlation = 0.4881397
🔍 Coarse search lag 111: correlation = 0.36428338
🔍 Coarse search lag 135: correlation = 0.35500216
🔍 Fine search around lag 72 in range [67, 77]
🔍 Fine search lag 67: correlation = 0.42189202
🔍 Fine search lag 68: correlation = 0.47213987
🔍 Fine search lag 69: correlation = 0.5621134
🔍 Fine search lag 70: correlation = 0.64259404
🔍 Fine search lag 71: correlation = 0.7430499
🔍 Fine search lag 72: correlation = 0.7271098
🔍 Fine search lag 73: correlation = 0.6807864
🔍 Fine search lag 74: correlation = 0.62399215
🔍 Fine search lag 75: correlation = 0.6028435
🔍 Fine search lag 76: correlation = 0.61336505
🔍 Fine search lag 77: correlation = 0.62584025
🔍 Best correlation: 0.7430499 at period 71 (threshold: 0.6) [strategy: fallback]
🎯 Target range updated: 10.347801-17.246336 Hz (periods: 56-94)
🔄 Run length reduced: 3 → 1 (leap: 5.15361 Hz, threshold: 1.7286917 Hz)
📊 Moving averages updated - Period: 92.166664, Amplitude: 0.72418463, RunLength: 2
✅ Pitch detected: 13.797069 Hz (updating economical state and target range)
🔧 Step 5 complete - Detected pitch: 13.797069 Hz
🎵 Detected pitch: 13.797069 Hz (total samples: 436800)
� Calculated pressure: 10.779919 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 13.797069, Pressure: 10.779919, Breaths: 1
🎤 Audio Level: 0.0006643109, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 441600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 2.451096e-09, Max: 7.175323e-05, Avg: 3.020463e-06, Variance: 8.31148e-11
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 441600)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.0007311868, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 446400
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 2.451096e-09, Max: 4.8769726e-07, Avg: 3.242234e-08, Variance: 3.761283e-15
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 446400)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.00060666155, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 451200
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 2.451096e-09, Max: 3.1183677e-07, Avg: 1.7648563e-08, Variance: 5.0367337e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 451200)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🫁 Breath detection: Entering ending state (silence: 0.3s)
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 1
🎤 Audio Level: 0.00066415337, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 456000
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 9.332083e-10, Max: 1.0014829e-07, Avg: 1.8391452e-08, Variance: 1.8038183e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 456000)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🫁 Breath detection: Completed breath #2 (duration: 2.0s)
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 2
🎤 Audio Level: 0.00086571753, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 460800
🫁 Recorded breath duration: 2.0s (Total: 2)
🫁 Breath completed for step 2: duration=2.0s, performance=completedAmber
🛑 stopRecording called
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 9.332083e-10, Max: 1.319662e-07, Avg: 2.1413975e-08, Variance: 3.674742e-16
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 460800)
� Calculated pressure: 0.0 cm H2O
📊 processAudioBuffer called - frameLength: 4800
🎤 Audio Level: 0.0034066858, Samples: 4800
🔍 Processing audio through pitch detector...
🔧 PitchDetector.processChunk - Input samples: 4800, Absolute index: 465600
🔧 Step 1 complete - Filtered samples: 4800
🔧 Step 2 complete - Squared samples: 4800
🔧 Step 3 complete - Downsampled samples: 97
🔧 Gaussian smoothing - sigma: 0.008166667, sigmaDownsampled: 0.4001667, kernelSize: 3
🔧 Step 4 complete - Smoothed samples: 97
🔧 Accumulated buffer length: 300 (target: 300)
🔍 Autocorrelation - Data length: 300, MinPeriod: 24, MaxPeriod: 139
🔍 Data stats - Min: 9.332083e-10, Max: 1.8425968e-06, Avg: 3.1742967e-08, Variance: 1.3924398e-14
❌ No signal variance - data is too flat for correlation
🔧 Step 5 complete - Detected pitch: 0.0 Hz
🎵 Detected pitch: 0.0 Hz (total samples: 465600)
� Calculated pressure: 0.0 cm H2O
✅ Audio engine stopped
💾 Saved 7 recent sessions to file
✅ Session saved to history: 00:10, Quality: poor
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 2
🖥️ UI updated - Freq: 0.0, Pressure: 0.0, Breaths: 2
App is being debugged, do not track this hang
Hang detected: 0.27s (debugger attached, not reporting)